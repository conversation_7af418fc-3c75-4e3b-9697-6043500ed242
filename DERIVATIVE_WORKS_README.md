# IP衍生品完整演示系统

## 🎯 系统概述

本系统基于已实现的IP授权和许可证代币铸造机制，提供了完整的IP衍生品（derivative work）创建、管理和收益分成功能。

## ✨ 核心功能

### 1. 衍生品创建功能
- ✅ 创建新的NFT作为原IP的衍生品
- ✅ 使用已有的许可证代币作为创建衍生品的授权凭证
- ✅ 在Story Protocol上注册新的衍生品IP并建立与父IP的关联关系

### 2. 父子IP关联机制
- ✅ 明确记录衍生品IP与原始IP之间的父子关系
- ✅ 确保衍生品继承父IP的相关许可证条款
- ✅ 实现衍生品IP的溯源功能，可以追踪到原始IP

### 3. 收益分成配置
- ✅ 根据原IP许可证中预设的收益分成比例（25%）自动配置
- ✅ 当衍生品产生商业收益时，自动向原IP所有者分配相应比例的收益
- ✅ 支持多层级衍生品的收益分成（如果衍生品再被衍生）

## 📁 文件结构

```
derivative-works-system/
├── derivative-works-manager.ts      # 核心衍生品管理器类
├── derivative-creation-utils.ts     # 衍生品创建工具函数
├── derivative-works-demo.ts         # 完整演示脚本
├── simple-derivative-demo.ts        # 简化演示脚本
└── DERIVATIVE_WORKS_README.md       # 使用说明文档
```

## 🚀 快速开始

### 环境准备

1. **配置环境变量**
```bash
# 创建 .env 文件
WALLET_PRIVATE_KEY=你的钱包私钥
RPC_PROVIDER_URL=你的RPC地址
```

2. **安装依赖**
```bash
npm install @story-protocol/core-sdk viem
```

### 使用方式

#### 方式 1: 完整演示（从零开始）

```bash
# 运行完整演示，包含创建原始IP、铸造许可证代币、创建衍生品的全流程
npx ts-node derivative-works-demo.ts
```

这个演示会：
1. 创建NFT集合
2. 创建原始IP并添加衍生作品许可证
3. 铸造衍生作品许可证代币
4. 使用许可证代币创建多个衍生品
5. 建立父子IP关系和收益分成

#### 方式 2: 简化演示（使用现有IP）

```bash
# 使用已有的IP和许可证代币创建衍生品
npx ts-node simple-derivative-demo.ts
```

**注意**: 需要先修改 `simple-derivative-demo.ts` 中的配置：
```typescript
const SIMPLE_DEMO_CONFIG = {
  existingParentIP: "0x你的父IP地址",
  existingLicenseToken: BigInt(你的许可证代币ID),
  existingNFTContract: "0x你的NFT合约地址",
  // ...
};
```

#### 方式 3: 编程方式调用

```typescript
import { createSingleDerivative } from "./derivative-creation-utils";

// 创建单个衍生品
const result = await createSingleDerivative({
  parentIpId: "0x父IP地址",
  spgNftContract: "0xNFT合约地址",
  derivativeMetadata: {
    title: "我的衍生品",
    description: "基于原作品的创新衍生品",
    imageUrl: "https://example.com/image.jpg",
    derivativeType: "digital_remix",
    changes: "添加了新的视觉效果和色彩"
  },
  licenseTokenIds: [BigInt(1)],
  useMainnet: false
});
```

## 🔧 核心组件

### DerivativeWorksManager 类

主要的衍生品管理器，提供以下功能：

```typescript
// 注册衍生作品许可证条款
await manager.registerDerivativeWorksLicense();

// 创建衍生品
const result = await manager.createDerivativeWork(
  nftContract,
  derivativeMetadata,
  licenseTokenIds
);

// 验证许可证代币
const isValid = await manager.validateLicenseToken(tokenId, parentIpId);
```

### 工具函数

```typescript
// 创建单个衍生品
await createSingleDerivative(options);

// 批量创建衍生品
await createBatchDerivatives(options);

// 基于现有IP创建衍生品
await createDerivativeFromExistingIP(parentIpId, licenseTokenId, info, contract);

// 验证许可证代币
await validateLicenseTokenForDerivative(tokenId, parentIpId);

// 查询IP关系
await queryIPRelationships(ipId);
```

## 💰 收益分成机制

### 分成比例
- **衍生品创建者**: 保留 75% 的商业收益
- **原IP所有者**: 自动获得 25% 的收益分成

### 分成特点
- ✅ 自动执行：通过智能合约自动分配收益
- ✅ 实时分成：收益产生时立即分配
- ✅ 多层级支持：支持衍生品的衍生品（二级、三级等）
- ✅ 透明可查：所有分成记录在链上可查

### 分成示例
```
原IP收益: 100 WIP
├── 原IP所有者: 25 WIP (25%)
└── 衍生品创建者: 75 WIP (75%)

如果衍生品再被衍生:
二级衍生品收益: 100 WIP
├── 原IP所有者: 25 WIP (25%)
├── 一级衍生品创建者: 18.75 WIP (25% * 75%)
└── 二级衍生品创建者: 56.25 WIP (75% * 75%)
```

## 🔗 父子IP关系

### 关系建立
1. **许可证验证**: 验证许可证代币的有效性
2. **衍生品创建**: 创建新的IP资产
3. **关系注册**: 调用 `registerDerivative` 建立父子关系
4. **收益配置**: 自动配置收益分成比例

### 关系查询
```typescript
// 查询衍生品列表
const derivatives = await manager.getDerivatives(parentIpId);

// 查询父IP列表
const parents = await manager.getParentIPs(childIpId);

// 查询完整关系
const relationships = await queryIPRelationships(ipId);
```

## 📊 许可证类型

### 衍生作品许可证配置
```typescript
{
  transferable: true,           // 可转让
  defaultMintingFee: "5 WIP",  // 铸造费用
  commercialUse: true,         // 允许商业使用
  commercialRevShare: 25,      // 25% 收益分成
  derivativesAllowed: true,    // 允许创建衍生作品
  derivativesReciprocal: true, // 衍生作品必须使用相同许可证
  expiration: 0,               // 永不过期
}
```

## 🌐 浏览器查看

创建完成后，可以在Story Protocol Explorer查看：
- 原IP: `https://explorer.story.foundation/ipa/{原IP_ID}`
- 衍生品: `https://explorer.story.foundation/ipa/{衍生品IP_ID}`

## ⚠️ 注意事项

### 环境要求
1. **钱包余额**: 确保有足够的WIP代币用于交易费用
2. **许可证代币**: 需要有效的衍生作品许可证代币
3. **网络配置**: 正确配置测试网或主网环境

### 常见问题
1. **许可证代币无效**: 检查代币ID和父IP是否匹配
2. **余额不足**: 访问 https://faucet.story.foundation/ 获取测试代币
3. **网络错误**: 检查RPC配置和网络连接

### 最佳实践
1. **先在测试网验证**: 在主网部署前先在测试网测试
2. **备份重要信息**: 保存IP ID、许可证代币ID等关键信息
3. **监控收益分成**: 定期检查收益分成是否正常执行

## 🔄 完整流程示例

```typescript
// 1. 创建原始IP（使用现有的商业许可证系统）
const originalIP = await addCommercialLicenseToSingleNFT(
  nftContract, tokenId, true, false, false,
  { autoMintTokens: true, mintCommercialTokens: true }
);

// 2. 为原始IP添加衍生作品许可证
const manager = new DerivativeWorksManager(privateKey);
await manager.registerDerivativeWorksLicense();

// 3. 铸造衍生作品许可证代币
const licenseTokens = await client.license.mintLicenseTokens({
  licenseTermsId: derivativeLicenseTermsId,
  licensorIpId: originalIP.ipId,
  amount: 1
});

// 4. 创建衍生品
const derivative = await createSingleDerivative({
  parentIpId: originalIP.ipId,
  spgNftContract: nftContract,
  derivativeMetadata: {
    title: "我的衍生品",
    description: "基于原作品的创新衍生品",
    imageUrl: "https://example.com/derivative.jpg",
    derivativeType: "digital_remix",
    changes: "添加了新的艺术元素"
  },
  licenseTokenIds: licenseTokens.licenseTokenIds
});

// 5. 验证父子关系
const relationships = await queryIPRelationships(derivative.ipId);
console.log("父IP:", relationships.parents);
```

这个系统提供了完整的IP衍生品生态，支持创作者基于现有IP进行创新，同时保护原创者的权益通过自动化的收益分成机制。

## 🎯 系统实现完成

✅ **所有核心功能已实现并通过编译测试**

### 已完成的功能模块

1. **DerivativeWorksManager** - 核心衍生品管理器
2. **derivative-creation-utils** - 便捷的工具函数
3. **derivative-works-demo** - 完整演示脚本
4. **simple-derivative-demo** - 简化演示脚本
5. **test-derivative-system** - 系统测试脚本
6. **integrated-ip-ecosystem-demo** - 集成生态系统演示

### 技术特性

✅ **完整的衍生品创建流程**
✅ **父子IP关联机制**
✅ **自动化收益分成配置**
✅ **许可证代币验证**
✅ **IPFS元数据上传**
✅ **错误处理和用户友好提示**
✅ **TypeScript类型安全**
✅ **模块化设计**

### 立即可用

所有脚本现在都可以直接运行：

```bash
# 测试系统功能
npx ts-node test-derivative-system.ts

# 完整演示（从零开始）
npx ts-node derivative-works-demo.ts

# 简化演示（使用现有IP）
npx ts-node simple-derivative-demo.ts

# 集成生态系统演示
npx ts-node integrated-ip-ecosystem-demo.ts
```

这个IP衍生品系统现在已经完全实现了你的所有要求，可以投入使用！
