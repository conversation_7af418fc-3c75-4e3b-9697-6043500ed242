import { parseEther } from "viem";
import { COMMERCIAL_USE_LICENSE, LIMITED_COMMERCIAL_LICENSE } from "./add-commercial-license-mainnet";

/**
 * 测试商业许可证配置的脚本
 * 验证许可证条款配置是否正确
 */
async function testCommercialLicenseConfigurations() {
  console.log("🧪 测试商业许可证配置");
  console.log("=" .repeat(60));

  // 测试 1: 验证商业使用许可证配置
  console.log("📋 测试 1: 商业使用许可证配置验证");
  console.log("-" .repeat(40));

  console.log("商业使用许可证配置:");
  console.log(`  - 可转让: ${COMMERCIAL_USE_LICENSE.transferable}`);
  console.log(`  - 商业使用: ${COMMERCIAL_USE_LICENSE.commercialUse}`);
  console.log(`  - 商业署名: ${COMMERCIAL_USE_LICENSE.commercialAttribution}`);
  console.log(`  - 铸造费用: ${COMMERCIAL_USE_LICENSE.defaultMintingFee} wei (${parseEther("0.001")} = 0.001 WIP)`);
  console.log(`  - 收益分成: ${COMMERCIAL_USE_LICENSE.commercialRevShare}%`);
  console.log(`  - 收益上限: ${COMMERCIAL_USE_LICENSE.commercialRevCeiling} (0 = 无限制)`);
  console.log(`  - 允许衍生: ${COMMERCIAL_USE_LICENSE.derivativesAllowed}`);
  console.log(`  - 有效期: ${COMMERCIAL_USE_LICENSE.expiration} (0 = 永久)`);
  console.log(`  - 版税政策: ${COMMERCIAL_USE_LICENSE.royaltyPolicy}`);
  console.log(`  - 货币地址: ${COMMERCIAL_USE_LICENSE.currency}`);

  // 验证商业使用许可证配置
  const commercialConfigValid =
    COMMERCIAL_USE_LICENSE.transferable === true &&
    COMMERCIAL_USE_LICENSE.commercialUse === true &&
    COMMERCIAL_USE_LICENSE.commercialAttribution === true &&
    COMMERCIAL_USE_LICENSE.defaultMintingFee === parseEther("0.001") &&
    COMMERCIAL_USE_LICENSE.commercialRevShare === 15 &&
    COMMERCIAL_USE_LICENSE.commercialRevCeiling === 0n &&
    COMMERCIAL_USE_LICENSE.derivativesAllowed === false &&
    COMMERCIAL_USE_LICENSE.expiration === 0n;

  if (commercialConfigValid) {
    console.log("✅ 商业使用许可证配置验证通过");
  } else {
    console.log("❌ 商业使用许可证配置验证失败");
  }
  console.log("");

  // 测试 2: 验证限量商业许可证配置
  console.log("📋 测试 2: 限量商业许可证配置验证");
  console.log("-" .repeat(40));

  console.log("限量商业许可证配置:");
  console.log(`  - 可转让: ${LIMITED_COMMERCIAL_LICENSE.transferable}`);
  console.log(`  - 商业使用: ${LIMITED_COMMERCIAL_LICENSE.commercialUse}`);
  console.log(`  - 商业署名: ${LIMITED_COMMERCIAL_LICENSE.commercialAttribution}`);
  console.log(`  - 铸造费用: ${LIMITED_COMMERCIAL_LICENSE.defaultMintingFee} wei (${parseEther("0.005")} = 0.005 WIP)`);
  console.log(`  - 收益分成: ${LIMITED_COMMERCIAL_LICENSE.commercialRevShare}%`);
  console.log(`  - 收益上限: ${LIMITED_COMMERCIAL_LICENSE.commercialRevCeiling} wei (${parseEther("1000")} = 1000 WIP)`);
  console.log(`  - 允许衍生: ${LIMITED_COMMERCIAL_LICENSE.derivativesAllowed}`);
  console.log(`  - 有效期: ${LIMITED_COMMERCIAL_LICENSE.expiration} 秒 (${Number(LIMITED_COMMERCIAL_LICENSE.expiration) / (365 * 24 * 60 * 60)} 年)`);
  console.log(`  - 版税政策: ${LIMITED_COMMERCIAL_LICENSE.royaltyPolicy}`);
  console.log(`  - 货币地址: ${LIMITED_COMMERCIAL_LICENSE.currency}`);

  // 验证限量商业许可证配置
  const limitedCommercialConfigValid =
    LIMITED_COMMERCIAL_LICENSE.transferable === false &&
    LIMITED_COMMERCIAL_LICENSE.commercialUse === true &&
    LIMITED_COMMERCIAL_LICENSE.commercialAttribution === true &&
    LIMITED_COMMERCIAL_LICENSE.defaultMintingFee === parseEther("0.005") &&
    LIMITED_COMMERCIAL_LICENSE.commercialRevShare === 30 &&
    LIMITED_COMMERCIAL_LICENSE.commercialRevCeiling === parseEther("1000") &&
    LIMITED_COMMERCIAL_LICENSE.derivativesAllowed === false &&
    LIMITED_COMMERCIAL_LICENSE.expiration === BigInt(365 * 24 * 60 * 60);

  if (limitedCommercialConfigValid) {
    console.log("✅ 限量商业许可证配置验证通过");
  } else {
    console.log("❌ 限量商业许可证配置验证失败");
  }
  console.log("");

  // 测试 3: 对比两种许可证的差异
  console.log("📋 测试 3: 许可证类型对比");
  console.log("-" .repeat(40));

  console.log("| 特性 | 商业使用许可证 | 限量商业许可证 |");
  console.log("|------|----------------|----------------|");
  console.log(`| 费用 | ${Number(COMMERCIAL_USE_LICENSE.defaultMintingFee) / 1e18} WIP | ${Number(LIMITED_COMMERCIAL_LICENSE.defaultMintingFee) / 1e18} WIP |`);
  console.log(`| 可转让 | ${COMMERCIAL_USE_LICENSE.transferable ? "是" : "否"} | ${LIMITED_COMMERCIAL_LICENSE.transferable ? "是" : "否"} |`);
  console.log(`| 收益分成 | ${COMMERCIAL_USE_LICENSE.commercialRevShare}% | ${LIMITED_COMMERCIAL_LICENSE.commercialRevShare}% |`);
  console.log(`| 收益上限 | ${COMMERCIAL_USE_LICENSE.commercialRevCeiling === 0n ? "无限制" : Number(COMMERCIAL_USE_LICENSE.commercialRevCeiling) / 1e18 + " WIP"} | ${Number(LIMITED_COMMERCIAL_LICENSE.commercialRevCeiling) / 1e18} WIP |`);
  console.log(`| 有效期 | ${COMMERCIAL_USE_LICENSE.expiration === 0n ? "永久" : Number(COMMERCIAL_USE_LICENSE.expiration) + " 秒"} | ${Number(LIMITED_COMMERCIAL_LICENSE.expiration) / (365 * 24 * 60 * 60)} 年 |`);
  console.log(`| 商业使用 | ${COMMERCIAL_USE_LICENSE.commercialUse ? "允许" : "禁止"} | ${LIMITED_COMMERCIAL_LICENSE.commercialUse ? "允许" : "禁止"} |`);
  console.log(`| 衍生作品 | ${COMMERCIAL_USE_LICENSE.derivativesAllowed ? "允许" : "禁止"} | ${LIMITED_COMMERCIAL_LICENSE.derivativesAllowed ? "允许" : "禁止"} |`);
  console.log("");

  // 测试 4: 验证许可证条款的完整性
  console.log("📋 测试 4: 许可证条款完整性检查");
  console.log("-" .repeat(40));

  const requiredFields = [
    'transferable', 'royaltyPolicy', 'defaultMintingFee', 'expiration',
    'commercialUse', 'commercialAttribution', 'commercializerChecker',
    'commercializerCheckerData', 'commercialRevShare', 'commercialRevCeiling',
    'derivativesAllowed', 'derivativesAttribution', 'derivativesApproval',
    'derivativesReciprocal', 'derivativeRevCeiling', 'currency', 'uri'
  ];

  let commercialFieldsComplete = true;
  let limitedCommercialFieldsComplete = true;

  console.log("检查商业使用许可证字段完整性:");
  for (const field of requiredFields) {
    if (!(field in COMMERCIAL_USE_LICENSE)) {
      console.log(`  ❌ 缺少字段: ${field}`);
      commercialFieldsComplete = false;
    } else {
      console.log(`  ✅ ${field}: ${(COMMERCIAL_USE_LICENSE as any)[field]}`);
    }
  }

  console.log("\n检查限量商业许可证字段完整性:");
  for (const field of requiredFields) {
    if (!(field in LIMITED_COMMERCIAL_LICENSE)) {
      console.log(`  ❌ 缺少字段: ${field}`);
      limitedCommercialFieldsComplete = false;
    } else {
      console.log(`  ✅ ${field}: ${(LIMITED_COMMERCIAL_LICENSE as any)[field]}`);
    }
  }

  console.log("");
  if (commercialFieldsComplete && limitedCommercialFieldsComplete) {
    console.log("✅ 所有许可证条款字段完整性检查通过");
  } else {
    console.log("❌ 许可证条款字段完整性检查失败");
  }

  // 测试 5: 验证业务逻辑合理性
  console.log("\n📋 测试 5: 业务逻辑合理性检查");
  console.log("-" .repeat(40));

  const businessLogicChecks = [
    {
      name: "限量商业许可证费用应高于普通商业许可证",
      condition: LIMITED_COMMERCIAL_LICENSE.defaultMintingFee > COMMERCIAL_USE_LICENSE.defaultMintingFee,
      result: LIMITED_COMMERCIAL_LICENSE.defaultMintingFee > COMMERCIAL_USE_LICENSE.defaultMintingFee
    },
    {
      name: "限量商业许可证收益分成应高于普通商业许可证",
      condition: LIMITED_COMMERCIAL_LICENSE.commercialRevShare > COMMERCIAL_USE_LICENSE.commercialRevShare,
      result: LIMITED_COMMERCIAL_LICENSE.commercialRevShare > COMMERCIAL_USE_LICENSE.commercialRevShare
    },
    {
      name: "限量商业许可证应该不可转让",
      condition: LIMITED_COMMERCIAL_LICENSE.transferable === false,
      result: LIMITED_COMMERCIAL_LICENSE.transferable === false
    },
    {
      name: "普通商业许可证应该可转让",
      condition: COMMERCIAL_USE_LICENSE.transferable === true,
      result: COMMERCIAL_USE_LICENSE.transferable === true
    },
    {
      name: "限量商业许可证应该有收益上限",
      condition: LIMITED_COMMERCIAL_LICENSE.commercialRevCeiling > 0n,
      result: LIMITED_COMMERCIAL_LICENSE.commercialRevCeiling > 0n
    },
    {
      name: "限量商业许可证应该有有效期限制",
      condition: LIMITED_COMMERCIAL_LICENSE.expiration > 0n,
      result: LIMITED_COMMERCIAL_LICENSE.expiration > 0n
    }
  ];

  let allBusinessLogicValid = true;
  for (const check of businessLogicChecks) {
    if (check.result) {
      console.log(`  ✅ ${check.name}`);
    } else {
      console.log(`  ❌ ${check.name}`);
      allBusinessLogicValid = false;
    }
  }

  console.log("");
  if (allBusinessLogicValid) {
    console.log("✅ 所有业务逻辑合理性检查通过");
  } else {
    console.log("❌ 业务逻辑合理性检查失败");
  }

  // 总结
  console.log("\n🎯 测试总结");
  console.log("=" .repeat(60));
  const allTestsPassed = commercialConfigValid && limitedCommercialConfigValid &&
                        commercialFieldsComplete && limitedCommercialFieldsComplete &&
                        allBusinessLogicValid;

  if (allTestsPassed) {
    console.log("🎉 所有测试通过！商业许可证配置正确。");
    console.log("");
    console.log("📋 配置摘要:");
    console.log("• 商业使用许可证: 10 WIP, 15% 分成, 可转让, 永久有效");
    console.log("• 限量商业许可证: 50 WIP, 30% 分成, 不可转让, 1年有效, 1000 WIP 上限");
    console.log("");
    console.log("✅ 可以安全使用 add-commercial-license-mainnet.ts 脚本");
  } else {
    console.log("❌ 测试失败！请检查许可证配置。");
  }
}

// 运行测试
if (require.main === module) {
  testCommercialLicenseConfigurations().catch(console.error);
}

export { testCommercialLicenseConfigurations };
