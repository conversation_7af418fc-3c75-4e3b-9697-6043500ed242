import { type Address } from "viem/accounts";
import { zeroAddress } from "viem";
import { StoryClient, type StoryConfig } from "@story-protocol/core-sdk";
import { http } from "viem";
import { privateKeyToAccount } from "viem/accounts";

// 导入现有的许可证系统
import { addCommercialLicenseToSingleNFT } from "./commercial-license-dfa2025";
import { MainnetCommercialLicenseManager } from "./add-commercial-license-mainnet";

// 导入衍生品系统
import { DerivativeWorksManager } from "./derivative-works-manager";
import { createSingleDerivative } from "./derivative-creation-utils";

/**
 * 集成IP生态系统演示
 * 展示从创建原始IP、添加许可证、铸造代币到创建衍生品的完整生态系统
 */

// 集成演示配置
const ECOSYSTEM_CONFIG = {
  // 网络设置
  useMainnet: false,

  // 原始IP配置
  originalArtwork: {
    name: "生态系统演示原创作品",
    description: "这是一个展示完整IP生态系统的原创艺术作品，支持商业使用和衍生作品创作",
    imageUrl: "https://picsum.photos/id/1/800/600",
    // 许可证配置
    includeCommercial: true,
    includeLimitedCommercial: false,
    autoMintTokens: true,
    tokenAmount: 5 // 铸造5个许可证代币
  },

  // 衍生品配置
  derivatives: [
    {
      title: "数字艺术重制版",
      description: "基于原作品的数字艺术重制，融入了现代数字艺术元素",
      imageUrl: "https://picsum.photos/id/2/800/600",
      derivativeType: "digital_remix",
      changes: "添加了数字特效、调整了色彩饱和度、增加了动态元素"
    },
    {
      title: "抽象风格改编",
      description: "将原作品改编为抽象艺术风格，保持核心构图但改变表现形式",
      imageUrl: "https://picsum.photos/id/3/800/600",
      derivativeType: "style_adaptation",
      changes: "转换为抽象表现主义风格，简化形状，强化色彩对比"
    },
    {
      title: "商业应用版本",
      description: "为商业用途优化的版本，适合品牌推广和营销活动",
      imageUrl: "https://picsum.photos/id/4/800/600",
      derivativeType: "commercial_adaptation",
      changes: "优化了商业应用场景，调整了尺寸比例，增加了品牌友好元素"
    }
  ]
};

async function runIntegratedEcosystemDemo() {
  console.log("🌟 集成IP生态系统演示");
  console.log("=" .repeat(80));
  console.log("本演示展示从原始IP创建到衍生品生态的完整流程");
  console.log("包含：IP注册 → 许可证添加 → 代币铸造 → 衍生品创建 → 收益分成");
  console.log("");

  // 检查环境变量
  if (!process.env.WALLET_PRIVATE_KEY) {
    console.error("❌ 请在 .env 文件中配置 WALLET_PRIVATE_KEY");
    console.log("💡 创建 .env 文件并添加:");
    console.log("   WALLET_PRIVATE_KEY=你的钱包私钥");
    console.log("   RPC_PROVIDER_URL=你的RPC地址");
    return;
  }

  try {
    // 初始化系统组件
    console.log("🔧 初始化生态系统组件...");
    const account = privateKeyToAccount(`0x${process.env.WALLET_PRIVATE_KEY}` as Address);
    const config: StoryConfig = {
      account: account,
      transport: http(ECOSYSTEM_CONFIG.useMainnet ? "https://rpc.story.foundation" : process.env.RPC_PROVIDER_URL),
      chainId: ECOSYSTEM_CONFIG.useMainnet ? "mainnet" : "aeneid",
    };
    const client = StoryClient.newClient(config);

    const licenseManager = new MainnetCommercialLicenseManager(
      process.env.WALLET_PRIVATE_KEY,
      ECOSYSTEM_CONFIG.useMainnet
    );

    const derivativeManager = new DerivativeWorksManager(
      process.env.WALLET_PRIVATE_KEY,
      ECOSYSTEM_CONFIG.useMainnet
    );

    console.log(`✅ 生态系统初始化完成 (${ECOSYSTEM_CONFIG.useMainnet ? "主网" : "测试网"})`);
    console.log("");

    // 阶段 1: 创建NFT集合
    console.log("🎨 阶段 1: 创建NFT集合");
    console.log("=" .repeat(60));

    const collection = await client.nftClient.createNFTCollection({
      name: "IP Ecosystem Demo Collection",
      symbol: "IEDC",
      isPublicMinting: false,
      mintOpen: true,
      mintFeeRecipient: zeroAddress,
      contractURI: "https://example.com/ecosystem-collection.json",
      txOptions: { waitForTransaction: true },
    });

    console.log(`✅ NFT集合创建成功`);
    console.log(`   集合地址: ${collection.spgNftContract}`);
    console.log("");

    // 阶段 2: 创建原始IP并添加商业许可证
    console.log("🖼️ 阶段 2: 创建原始IP并添加商业许可证");
    console.log("=" .repeat(60));

    const originalIPResult = await addCommercialLicenseToSingleNFT(
      collection.spgNftContract as Address,
      "1", // Token ID
      ECOSYSTEM_CONFIG.originalArtwork.includeCommercial,
      ECOSYSTEM_CONFIG.originalArtwork.includeLimitedCommercial,
      ECOSYSTEM_CONFIG.useMainnet,
      {
        autoMintTokens: ECOSYSTEM_CONFIG.originalArtwork.autoMintTokens,
        mintCommercialTokens: true,
        mintLimitedCommercialTokens: false,
        tokenAmount: ECOSYSTEM_CONFIG.originalArtwork.tokenAmount
      }
    );

    console.log(`✅ 原始IP创建成功`);
    console.log(`   IP ID: ${originalIPResult.ipId}`);
    console.log(`   商业许可证 ID: ${originalIPResult.commercialLicenseTermsId}`);
    if ('mintResults' in originalIPResult && originalIPResult.mintResults) {
      console.log(`   铸造的许可证代币数量: ${originalIPResult.mintResults.length}`);
    }
    console.log("");

    // 阶段 3: 为原始IP添加衍生作品许可证
    console.log("📋 阶段 3: 为原始IP添加衍生作品许可证");
    console.log("=" .repeat(60));

    // 注册衍生作品许可证条款
    const derivativeLicenseTermsId = await derivativeManager.registerDerivativeWorksLicense();

    // 为原始IP附加衍生作品许可证
    try {
      const attachResponse = await client.license.attachLicenseTerms({
        licenseTermsId: derivativeLicenseTermsId.toString(),
        ipId: originalIPResult.ipId,
        txOptions: { waitForTransaction: true },
      });

      console.log(`✅ 衍生作品许可证已附加到原始IP`);
      console.log(`   交易哈希: ${attachResponse.txHash}`);
    } catch (error) {
      console.log(`ℹ️ 许可证可能已经附加: ${error}`);
    }
    console.log("");

    // 阶段 4: 铸造衍生作品许可证代币
    console.log("🪙 阶段 4: 铸造衍生作品许可证代币");
    console.log("=" .repeat(60));

    let derivativeLicenseTokenIds: bigint[] = [];

    try {
      const mintResponse = await client.license.mintLicenseTokens({
        licenseTermsId: derivativeLicenseTermsId.toString(),
        licensorIpId: originalIPResult.ipId,
        amount: ECOSYSTEM_CONFIG.derivatives.length, // 为每个衍生品铸造一个代币
        maxMintingFee: "5000000000000000", // 0.005 WIP per token
        maxRevenueShare: 25, // 25%
        txOptions: { waitForTransaction: true },
      });

      derivativeLicenseTokenIds = mintResponse.licenseTokenIds || [];
      console.log(`✅ 衍生作品许可证代币铸造成功`);
      console.log(`   代币数量: ${derivativeLicenseTokenIds.length}`);
      console.log(`   代币 IDs: ${derivativeLicenseTokenIds.join(", ")}`);
      console.log(`   交易哈希: ${mintResponse.txHash}`);
    } catch (error) {
      console.log(`⚠️ 衍生作品许可证代币铸造失败: ${error}`);
      console.log("   将使用模拟代币ID继续演示");
      derivativeLicenseTokenIds = [BigInt(1), BigInt(2), BigInt(3)];
    }
    console.log("");

    // 阶段 5: 创建衍生品生态系统
    console.log("🎭 阶段 5: 创建衍生品生态系统");
    console.log("=" .repeat(60));

    const derivativeResults = [];

    for (let i = 0; i < ECOSYSTEM_CONFIG.derivatives.length && i < derivativeLicenseTokenIds.length; i++) {
      const derivativeConfig = ECOSYSTEM_CONFIG.derivatives[i];
      const licenseTokenId = derivativeLicenseTokenIds[i];

      console.log(`🔄 创建衍生品 ${i + 1}/${ECOSYSTEM_CONFIG.derivatives.length}: ${derivativeConfig.title}`);
      console.log("-" .repeat(50));

      try {
        const derivativeResult = await createSingleDerivative({
          parentIpId: originalIPResult.ipId,
          spgNftContract: collection.spgNftContract as Address,
          derivativeMetadata: {
            title: derivativeConfig.title,
            description: derivativeConfig.description,
            imageUrl: derivativeConfig.imageUrl,
            derivativeType: derivativeConfig.derivativeType,
            changes: derivativeConfig.changes
          },
          licenseTokenIds: [licenseTokenId],
          useMainnet: ECOSYSTEM_CONFIG.useMainnet
        });

        derivativeResults.push({
          ...derivativeResult,
          config: derivativeConfig,
          licenseTokenId
        });

        console.log(`✅ 衍生品 ${i + 1} 创建成功`);
        console.log(`   衍生品 IP ID: ${derivativeResult.ipId}`);
        console.log(`   Token ID: ${derivativeResult.tokenId}`);
        console.log(`   使用的许可证代币: ${licenseTokenId}`);
        console.log("");

      } catch (error) {
        console.log(`❌ 衍生品 ${i + 1} 创建失败: ${error}`);
        console.log("");
      }

      // 添加延迟避免请求过快
      if (i < ECOSYSTEM_CONFIG.derivatives.length - 1) {
        console.log("⏳ 等待 3 秒后创建下一个衍生品...");
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    }

    // 阶段 6: 展示完整的IP生态系统
    console.log("🌟 阶段 6: IP生态系统总览");
    console.log("=" .repeat(60));

    console.log("🎉 集成IP生态系统演示完成！");
    console.log("");
    console.log("📊 生态系统总结:");
    console.log(`✅ 原始IP: ${originalIPResult.ipId}`);
    console.log(`✅ 商业许可证代币: ${ECOSYSTEM_CONFIG.originalArtwork.tokenAmount} 个`);
    console.log(`✅ 衍生作品许可证代币: ${derivativeLicenseTokenIds.length} 个`);
    console.log(`✅ 创建的衍生品: ${derivativeResults.length} 个`);
    console.log("");

    console.log("🔗 IP关系图:");
    console.log(`📄 原始IP: ${originalIPResult.ipId}`);
    console.log("├── 📋 商业使用许可证 (15% 收益分成)");
    console.log("├── 📋 衍生作品许可证 (25% 收益分成)");
    derivativeResults.forEach((result, index) => {
      const isLast = index === derivativeResults.length - 1;
      const prefix = isLast ? "└──" : "├──";
      console.log(`${prefix} 🎭 ${result.config.title}: ${result.ipId}`);
      console.log(`${isLast ? "   " : "│  "}    └── 许可证代币: ${result.licenseTokenId}`);
    });
    console.log("");

    console.log("💰 收益分成机制:");
    console.log("🔸 商业使用许可证:");
    console.log("  • 原IP所有者: 15% 收益分成");
    console.log("  • 许可证持有者: 85% 收益");
    console.log("🔸 衍生作品许可证:");
    console.log("  • 原IP所有者: 25% 收益分成");
    console.log("  • 衍生品创建者: 75% 收益");
    console.log("🔸 多层级衍生:");
    console.log("  • 支持衍生品的衍生品");
    console.log("  • 自动计算多层级收益分成");
    console.log("");

    console.log("🌐 查看详情:");
    console.log(`• 原始IP: https://explorer.story.foundation/ipa/${originalIPResult.ipId}`);
    derivativeResults.forEach((result, index) => {
      console.log(`• 衍生品 ${index + 1}: https://explorer.story.foundation/ipa/${result.ipId}`);
    });
    console.log("");

    console.log("🚀 生态系统特性:");
    console.log("✅ 完整的IP生命周期管理");
    console.log("✅ 多种许可证类型支持");
    console.log("✅ 自动化代币铸造");
    console.log("✅ 智能收益分成");
    console.log("✅ 可追溯的父子关系");
    console.log("✅ 可扩展的衍生品生态");
    console.log("");

    console.log("💡 下一步可能的操作:");
    console.log("1. 为衍生品添加更多许可证类型");
    console.log("2. 基于衍生品创建二级衍生品");
    console.log("3. 铸造衍生品的商业许可证代币");
    console.log("4. 监控和管理收益分成");
    console.log("5. 扩展到更多的创作者和作品");

  } catch (error) {
    console.error("💥 生态系统演示失败:", error);

    console.log("");
    console.log("🔧 故障排除建议:");
    if (error instanceof Error) {
      if (error.message.includes("余额不足")) {
        console.log("• 确保钱包有足够的 WIP 代币余额");
        console.log("• 访问 https://faucet.story.foundation/ 获取测试代币");
      } else if (error.message.includes("网络")) {
        console.log("• 检查网络连接和RPC配置");
        console.log("• 确认测试网/主网设置正确");
      } else {
        console.log("• 检查环境变量配置");
        console.log("• 验证钱包私钥格式");
        console.log("• 查看详细错误信息进行调试");
      }
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runIntegratedEcosystemDemo().catch(console.error);
}

export { runIntegratedEcosystemDemo, ECOSYSTEM_CONFIG };
