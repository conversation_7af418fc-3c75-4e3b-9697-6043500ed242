import { type Address } from "viem/accounts";
import { zeroAddress } from "viem";
import { DerivativeWorksManager, type DerivativeMetadata, uploadJSONToIPFS } from "./derivative-works-manager";
import { MainnetCommercialLicenseManager } from "./add-commercial-license-mainnet";
import { StoryClient, type StoryConfig } from "@story-protocol/core-sdk";
import { http } from "viem";
import { privateKeyToAccount } from "viem/accounts";
import { createHash } from "crypto";

/**
 * IP衍生品许可证和收益分成完整演示系统
 * 展示从创建原始IP、创建衍生品到为衍生品创建许可证代币并测试收益分成的完整流程
 */

// 演示配置
const DEMO_CONFIG = {
  // 网络设置
  useMainnet: false, // 设置为 true 使用主网

  // 原始IP配置
  originalIP: {
    name: "原创音乐作品 #001",
    description: "这是一个支持衍生作品创作的原创音乐作品，允许商业使用和收益分成",
    imageUrl: "https://picsum.photos/id/200/800/600",
    includeCommercial: true,
    includeLimitedCommercial: false,
    includeDerivativeWorks: true, // 关键：支持衍生作品
    autoMintTokens: true,
    tokenAmount: 2 // 铸造2个许可证代币用于演示
  },

  // 衍生品配置
  derivative: {
    title: "电子音乐混音版",
    description: "基于原创音乐作品创作的电子音乐混音版本，添加了现代电子元素",
    imageUrl: "https://picsum.photos/id/201/800/600",
    derivativeType: "electronic_remix",
    changes: "添加了电子合成器、鼓点和现代音效处理"
  },

  // 收益分成测试配置
  revenueTest: {
    derivativeLicenseTokenAmount: 3, // 铸造3个衍生品许可证代币
    expectedRevenueSharePercentage: 25 // 预期收益分成百分比
  }
};

async function runDerivativeLicenseRevenueDemo() {
  console.log("🎵 IP衍生品许可证和收益分成完整演示系统");
  console.log("=" .repeat(80));
  console.log("本演示展示从创建原始IP到衍生品许可证代币铸造和收益分成的完整流程");
  console.log("");

  // 检查环境变量
  if (!process.env.WALLET_PRIVATE_KEY) {
    console.error("❌ 请在 .env 文件中配置 WALLET_PRIVATE_KEY");
    console.log("💡 创建 .env 文件并添加:");
    console.log("   WALLET_PRIVATE_KEY=你的钱包私钥");
    console.log("   RPC_PROVIDER_URL=你的RPC地址");
    return;
  }

  try {
    // 初始化客户端和管理器
    console.log("🔧 初始化系统组件...");
    const account = privateKeyToAccount(`0x${process.env.WALLET_PRIVATE_KEY}` as Address);
    const config: StoryConfig = {
      account: account,
      transport: http(DEMO_CONFIG.useMainnet ? "https://rpc.story.foundation" : process.env.RPC_PROVIDER_URL),
      chainId: DEMO_CONFIG.useMainnet ? "mainnet" : "aeneid",
    };
    const client = StoryClient.newClient(config);

    const licenseManager = new MainnetCommercialLicenseManager(
      process.env.WALLET_PRIVATE_KEY,
      DEMO_CONFIG.useMainnet
    );

    const derivativeManager = new DerivativeWorksManager(
      process.env.WALLET_PRIVATE_KEY,
      DEMO_CONFIG.useMainnet
    );

    console.log(`✅ 系统初始化完成 (${DEMO_CONFIG.useMainnet ? "主网" : "测试网"})`);
    console.log(`   钱包地址: ${account.address}`);
    console.log("");

    // 步骤 1: 创建NFT集合
    console.log("🎨 步骤 1: 创建NFT集合");
    console.log("=" .repeat(60));

    const collection = await client.nftClient.createNFTCollection({
      name: "Music Derivative Works Collection",
      symbol: "MDWC",
      isPublicMinting: false,
      mintOpen: true,
      mintFeeRecipient: zeroAddress,
      contractURI: "https://example.com/music-derivative-collection.json",
      txOptions: { waitForTransaction: true },
    });

    console.log(`✅ NFT集合创建成功`);
    console.log(`   集合地址: ${collection.spgNftContract}`);
    console.log("");

    // 步骤 2: 创建原始IP并添加支持衍生作品的许可证
    console.log("🎼 步骤 2: 创建原始IP（支持衍生作品）");
    console.log("=" .repeat(60));

    // 注册衍生作品许可证条款
    await derivativeManager.registerDerivativeWorksLicense();

    // 创建原始IP
    const originalIPMetadata = client.ipAsset.generateIpMetadata({
      title: DEMO_CONFIG.originalIP.name,
      description: DEMO_CONFIG.originalIP.description,
      watermarkImg: DEMO_CONFIG.originalIP.imageUrl,
    });

    const originalNFTMetadata = {
      name: DEMO_CONFIG.originalIP.name,
      description: DEMO_CONFIG.originalIP.description,
      image: DEMO_CONFIG.originalIP.imageUrl,
    };

    // 上传元数据
    const ipIpfsHash = await uploadJSONToIPFS(originalIPMetadata);
    const ipHash = createHash("sha256")
      .update(JSON.stringify(originalIPMetadata))
      .digest("hex");
    const nftIpfsHash = await uploadJSONToIPFS(originalNFTMetadata);
    const nftHash = createHash("sha256")
      .update(JSON.stringify(originalNFTMetadata))
      .digest("hex");

    // 创建原始IP
    const originalIPResponse = await client.ipAsset.mintAndRegisterIp({
      spgNftContract: collection.spgNftContract as Address,
      allowDuplicates: true,
      ipMetadata: {
        ipMetadataURI: `https://ipfs.io/ipfs/${ipIpfsHash}`,
        ipMetadataHash: `0x${ipHash}`,
        nftMetadataURI: `https://ipfs.io/ipfs/${nftIpfsHash}`,
        nftMetadataHash: `0x${nftHash}`,
      },
      txOptions: { waitForTransaction: true },
    });

    console.log(`✅ 原始IP创建成功`);
    console.log(`   IP ID: ${originalIPResponse.ipId}`);
    console.log(`   Token ID: ${originalIPResponse.tokenId}`);
    console.log(`   交易哈希: ${originalIPResponse.txHash}`);
    console.log("");

    // 步骤 3: 为原始IP添加衍生作品许可证
    console.log("📋 步骤 3: 为原始IP添加衍生作品许可证");
    console.log("=" .repeat(60));

    const derivativeLicenseTermsId = derivativeManager.getDerivativeLicenseTermsId();
    if (derivativeLicenseTermsId && originalIPResponse.ipId) {
      try {
        const attachResponse = await client.license.attachLicenseTerms({
          licenseTermsId: derivativeLicenseTermsId.toString(),
          ipId: originalIPResponse.ipId,
          txOptions: { waitForTransaction: true },
        });

        console.log(`✅ 衍生作品许可证已附加到原始IP`);
        console.log(`   交易哈希: ${attachResponse.txHash}`);
      } catch (error) {
        console.log(`ℹ️ 许可证可能已经附加: ${error}`);
      }
    }
    console.log("");

    // 步骤 4: 创建衍生品
    console.log("🎭 步骤 4: 创建衍生品");
    console.log("=" .repeat(60));

    const derivativeMetadata: DerivativeMetadata = {
      title: DEMO_CONFIG.derivative.title,
      description: DEMO_CONFIG.derivative.description,
      imageUrl: DEMO_CONFIG.derivative.imageUrl,
      parentIpId: originalIPResponse.ipId!,
      derivativeType: DEMO_CONFIG.derivative.derivativeType,
      changes: DEMO_CONFIG.derivative.changes
    };

    const derivativeResult = await derivativeManager.createDerivativeWork(
      collection.spgNftContract as Address,
      derivativeMetadata,
      [] // 不使用许可证代币，直接使用许可证条款
    );

    console.log(`✅ 衍生品创建成功`);
    console.log(`   衍生品 IP ID: ${derivativeResult.ipId}`);
    console.log(`   父IP ID: ${originalIPResponse.ipId}`);
    console.log(`   交易哈希: ${derivativeResult.txHash}`);
    console.log("");

    // 步骤 5: 为衍生品创建商业许可证并铸造许可证代币
    console.log("🏪 步骤 5: 为衍生品创建商业许可证并测试收益分成");
    console.log("=" .repeat(60));

    await derivativeManager.testRevenueSharing(
      originalIPResponse.ipId!,
      derivativeResult.ipId,
      account.address
    );

    // 显示最终总结
    console.log("🎉 IP衍生品许可证和收益分成演示完成！");
    console.log("=" .repeat(80));
    console.log("");
    console.log("📊 演示总结:");
    console.log(`✅ 原始IP: ${originalIPResponse.ipId}`);
    console.log(`✅ 衍生品IP: ${derivativeResult.ipId}`);
    console.log(`✅ 衍生品许可证: 已创建并铸造代币`);
    console.log(`✅ 收益分成: 已测试`);
    console.log("");

    console.log("🔗 父子IP关系和收益分成:");
    console.log(`• 原始IP: ${originalIPResponse.ipId}`);
    console.log(`  └── 衍生品: ${derivativeResult.ipId}`);
    console.log(`      └── 衍生品许可证代币: 已铸造`);
    console.log(`      └── 收益分成: 25% → 原IP所有者`);
    console.log("");

    console.log("💰 收益分成机制:");
    console.log("• 当有人购买衍生品的许可证代币时：");
    console.log("  - 25% 的铸造费用自动分配给原IP所有者");
    console.log("  - 75% 的铸造费用归衍生品创建者所有");
    console.log("• 收益分成通过LAP版税政策智能合约自动执行");
    console.log("• 支持多级衍生品的递归收益分成");
    console.log("");

    console.log("🌐 查看详情:");
    console.log(`• 原始IP: https://explorer.story.foundation/ipa/${originalIPResponse.ipId}`);
    console.log(`• 衍生品IP: https://explorer.story.foundation/ipa/${derivativeResult.ipId}`);

  } catch (error) {
    console.error("💥 演示执行失败:", error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runDerivativeLicenseRevenueDemo().catch(console.error);
}

export { runDerivativeLicenseRevenueDemo, DEMO_CONFIG };
