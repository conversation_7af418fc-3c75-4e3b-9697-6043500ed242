import { type Address } from "viem/accounts";
import { 
  createSingleDerivative, 
  createDerivativeFromExistingIP,
  validateLicenseTokenForDerivative,
  queryIPRelationships 
} from "./derivative-creation-utils";

/**
 * 简化的衍生品创建演示
 * 展示如何使用工具函数快速创建衍生品
 */

// 简化演示配置
const SIMPLE_DEMO_CONFIG = {
  // 网络设置
  useMainnet: false,

  // 假设已有的父IP和许可证代币（需要替换为实际值）
  existingParentIP: "0x****************************************" as Address, // 替换为实际的父IP ID
  existingLicenseToken: BigInt(1), // 替换为实际的许可证代币ID
  existingNFTContract: "0xEe2E2a135eec6228562C77543643FD3BE6A174d1" as Address, // 替换为实际的NFT合约地址

  // 衍生品信息
  derivative: {
    title: "AI生成艺术衍生品",
    description: "基于原作品使用AI技术生成的衍生艺术品",
    imageUrl: "https://picsum.photos/id/200/800/600",
    derivativeType: "ai_generated",
    changes: "使用AI算法重新生成，保持原作品的核心元素但添加了新的视觉效果"
  }
};

async function runSimpleDerivativeDemo() {
  console.log("🎭 简化衍生品创建演示");
  console.log("=" .repeat(60));
  console.log("本演示展示如何使用现有的许可证代币创建衍生品");
  console.log("");

  // 检查环境变量
  if (!process.env.WALLET_PRIVATE_KEY) {
    console.error("❌ 请在 .env 文件中配置 WALLET_PRIVATE_KEY");
    console.log("💡 创建 .env 文件并添加:");
    console.log("   WALLET_PRIVATE_KEY=你的钱包私钥");
    console.log("   RPC_PROVIDER_URL=你的RPC地址");
    return;
  }

  // 检查配置
  if (SIMPLE_DEMO_CONFIG.existingParentIP.includes("**********")) {
    console.log("⚠️ 请先配置真实的父IP ID和许可证代币ID");
    console.log("💡 修改 SIMPLE_DEMO_CONFIG 中的以下字段:");
    console.log("   - existingParentIP: 父IP的实际ID");
    console.log("   - existingLicenseToken: 许可证代币的实际ID");
    console.log("   - existingNFTContract: NFT合约的实际地址");
    console.log("");
    console.log("📋 当前配置（示例值）:");
    console.log(`   父IP ID: ${SIMPLE_DEMO_CONFIG.existingParentIP}`);
    console.log(`   许可证代币ID: ${SIMPLE_DEMO_CONFIG.existingLicenseToken}`);
    console.log(`   NFT合约地址: ${SIMPLE_DEMO_CONFIG.existingNFTContract}`);
    console.log("");
    console.log("💡 你可以从之前的演示脚本输出中获取这些值");
    return;
  }

  try {
    console.log("📋 演示配置:");
    console.log(`   父IP ID: ${SIMPLE_DEMO_CONFIG.existingParentIP}`);
    console.log(`   许可证代币ID: ${SIMPLE_DEMO_CONFIG.existingLicenseToken}`);
    console.log(`   NFT合约地址: ${SIMPLE_DEMO_CONFIG.existingNFTContract}`);
    console.log(`   衍生品标题: ${SIMPLE_DEMO_CONFIG.derivative.title}`);
    console.log(`   网络: ${SIMPLE_DEMO_CONFIG.useMainnet ? "主网" : "测试网"}`);
    console.log("");

    // 步骤 1: 验证许可证代币
    console.log("🔍 步骤 1: 验证许可证代币");
    console.log("=" .repeat(50));
    
    const isValidToken = await validateLicenseTokenForDerivative(
      SIMPLE_DEMO_CONFIG.existingLicenseToken,
      SIMPLE_DEMO_CONFIG.existingParentIP,
      SIMPLE_DEMO_CONFIG.useMainnet
    );

    if (!isValidToken) {
      console.log("❌ 许可证代币验证失败，无法继续创建衍生品");
      return;
    }
    console.log("");

    // 步骤 2: 查询父IP的现有关系
    console.log("🔍 步骤 2: 查询父IP的现有关系");
    console.log("=" .repeat(50));
    
    const relationships = await queryIPRelationships(
      SIMPLE_DEMO_CONFIG.existingParentIP,
      SIMPLE_DEMO_CONFIG.useMainnet
    );

    console.log(`📊 父IP关系信息:`);
    console.log(`   现有衍生品数量: ${relationships.derivatives.length}`);
    console.log(`   父IP数量: ${relationships.parents.length}`);
    console.log("");

    // 步骤 3: 创建衍生品
    console.log("🎭 步骤 3: 创建衍生品");
    console.log("=" .repeat(50));
    
    const derivativeResult = await createDerivativeFromExistingIP(
      SIMPLE_DEMO_CONFIG.existingParentIP,
      SIMPLE_DEMO_CONFIG.existingLicenseToken,
      SIMPLE_DEMO_CONFIG.derivative,
      SIMPLE_DEMO_CONFIG.existingNFTContract,
      SIMPLE_DEMO_CONFIG.useMainnet
    );

    console.log("✅ 衍生品创建成功！");
    console.log("");

    // 步骤 4: 验证衍生品关系
    console.log("🔍 步骤 4: 验证衍生品关系");
    console.log("=" .repeat(50));
    
    const newRelationships = await queryIPRelationships(
      derivativeResult.ipId,
      SIMPLE_DEMO_CONFIG.useMainnet
    );

    console.log(`📊 新衍生品关系信息:`);
    console.log(`   衍生品IP ID: ${derivativeResult.ipId}`);
    console.log(`   父IP数量: ${newRelationships.parents.length}`);
    console.log(`   自身衍生品数量: ${newRelationships.derivatives.length}`);
    console.log("");

    // 显示最终总结
    console.log("🎉 简化衍生品创建演示完成！");
    console.log("=" .repeat(60));
    console.log("");
    console.log("📊 创建总结:");
    console.log(`✅ 父IP: ${SIMPLE_DEMO_CONFIG.existingParentIP}`);
    console.log(`✅ 衍生品IP: ${derivativeResult.ipId}`);
    console.log(`✅ 使用的许可证代币: ${SIMPLE_DEMO_CONFIG.existingLicenseToken}`);
    console.log(`✅ Token ID: ${derivativeResult.tokenId}`);
    console.log(`✅ 交易哈希: ${derivativeResult.txHash}`);
    console.log("");

    console.log("🔗 父子关系:");
    console.log(`衍生品: ${derivativeResult.ipId}`);
    console.log(`└── 父IP: ${SIMPLE_DEMO_CONFIG.existingParentIP}`);
    console.log(`└── 许可证代币: ${SIMPLE_DEMO_CONFIG.existingLicenseToken}`);
    console.log("");

    console.log("💰 收益分成:");
    console.log("• 当衍生品产生商业收益时，25% 自动分配给父IP所有者");
    console.log("• 衍生品创建者保留 75% 的收益");
    console.log("• 收益分成通过智能合约自动执行");
    console.log("");

    console.log("🌐 查看详情:");
    console.log(`• 父IP: https://explorer.story.foundation/ipa/${SIMPLE_DEMO_CONFIG.existingParentIP}`);
    console.log(`• 衍生品: https://explorer.story.foundation/ipa/${derivativeResult.ipId}`);
    console.log("");

    console.log("💡 下一步操作:");
    console.log("1. 可以为新创建的衍生品添加更多许可证类型");
    console.log("2. 可以基于这个衍生品再创建二级衍生品");
    console.log("3. 可以铸造衍生品的许可证代币供他人使用");
    console.log("4. 可以查看收益分成的实时状态");

  } catch (error) {
    console.error("💥 演示执行失败:", error);
    
    if (error instanceof Error) {
      if (error.message.includes("余额不足")) {
        console.log("");
        console.log("💡 解决建议:");
        console.log("• 确保钱包有足够的 WIP 代币余额");
        console.log("• 访问 https://faucet.story.foundation/ 获取测试代币");
      } else if (error.message.includes("许可证")) {
        console.log("");
        console.log("💡 解决建议:");
        console.log("• 检查许可证代币ID是否正确");
        console.log("• 确保许可证代币支持衍生作品创建");
        console.log("• 验证许可证代币是否已过期");
      }
    }
  }
}

// 配置更新函数
export function updateDemoConfig(config: {
  parentIP?: Address;
  licenseToken?: bigint;
  nftContract?: Address;
  useMainnet?: boolean;
}) {
  if (config.parentIP) {
    SIMPLE_DEMO_CONFIG.existingParentIP = config.parentIP;
  }
  if (config.licenseToken) {
    SIMPLE_DEMO_CONFIG.existingLicenseToken = config.licenseToken;
  }
  if (config.nftContract) {
    SIMPLE_DEMO_CONFIG.existingNFTContract = config.nftContract;
  }
  if (config.useMainnet !== undefined) {
    SIMPLE_DEMO_CONFIG.useMainnet = config.useMainnet;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runSimpleDerivativeDemo().catch(console.error);
}

export { runSimpleDerivativeDemo, SIMPLE_DEMO_CONFIG };
