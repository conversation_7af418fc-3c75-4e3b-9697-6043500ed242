import { type Address } from "viem/accounts";
import { parseEther, formatEther } from "viem";
import { StoryClient, type StoryConfig } from "@story-protocol/core-sdk";
import { http } from "viem";
import { privateKeyToAccount } from "viem/accounts";

/**
 * 简化版收益分成演示
 * 使用现有的IP和许可证条款来测试收益分成功能
 */

interface SimpleRevenueTestConfig {
  useMainnet: boolean;
  // 使用已知的IP ID进行测试
  parentIpId: Address;
  derivativeIpId: Address;
  // 使用已知的许可证条款ID
  licenseTermsId: string;
  // 测试参数
  tokenAmount: number;
  mintingFee: string; // 以WIP为单位
  revenueSharePercentage: number;
}

export class SimpleRevenueSharingDemo {
  private client: StoryClient;
  private account: any;

  constructor(privateKey: string, useMainnet: boolean = false) {
    this.account = privateKeyToAccount(`0x${privateKey}` as Address);

    const config: StoryConfig = {
      account: this.account,
      transport: http(useMainnet ? "https://rpc.story.foundation" : process.env.RPC_PROVIDER_URL),
      chainId: useMainnet ? "mainnet" : "aeneid",
    };

    this.client = StoryClient.newClient(config);
  }

  // 获取WIP代币余额
  async getWIPBalance(address: Address): Promise<bigint> {
    try {
      const balance = await this.client.wipClient.balanceOf(address);
      return balance;
    } catch (error) {
      console.log(`⚠️ 获取WIP余额失败: ${error}`);
      return BigInt(0);
    }
  }

  // 运行简化的收益分成测试
  async runSimpleRevenueTest(config: SimpleRevenueTestConfig): Promise<void> {
    console.log("💰 简化版收益分成测试");
    console.log("=" .repeat(80));
    console.log(`网络: ${config.useMainnet ? "主网" : "测试网"}`);
    console.log(`钱包地址: ${this.account.address}`);
    console.log(`父IP ID: ${config.parentIpId}`);
    console.log(`衍生品IP ID: ${config.derivativeIpId}`);
    console.log(`许可证条款ID: ${config.licenseTermsId}`);
    console.log(`测试代币数量: ${config.tokenAmount}`);
    console.log(`单个代币铸造费用: ${config.mintingFee} WIP`);
    console.log(`预期收益分成: ${config.revenueSharePercentage}%`);
    console.log("");

    try {
      // 1. 记录初始余额
      console.log("📊 步骤 1: 记录初始余额");
      console.log("-" .repeat(50));

      const initialBalance = await this.getWIPBalance(this.account.address);
      console.log(`钱包初始WIP余额: ${formatEther(initialBalance)} WIP`);
      console.log("");

      // 2. 检查IP是否存在
      console.log("🔍 步骤 2: 验证IP资产");
      console.log("-" .repeat(50));

      try {
        const parentIsRegistered = await this.client.ipAsset.isRegistered(config.parentIpId);
        const derivativeIsRegistered = await this.client.ipAsset.isRegistered(config.derivativeIpId);

        console.log(`父IP注册状态: ${parentIsRegistered ? "✅ 已注册" : "❌ 未注册"}`);
        console.log(`衍生品IP注册状态: ${derivativeIsRegistered ? "✅ 已注册" : "❌ 未注册"}`);

        if (!parentIsRegistered || !derivativeIsRegistered) {
          console.log("⚠️ 请确保IP ID正确且已注册");
          return;
        }
      } catch (error) {
        console.log(`ℹ️ IP验证跳过: ${error}`);
      }
      console.log("");

      // 3. 尝试为衍生品附加许可证条款
      console.log("📋 步骤 3: 附加许可证条款");
      console.log("-" .repeat(50));

      try {
        const attachResponse = await this.client.license.attachLicenseTerms({
          licenseTermsId: config.licenseTermsId,
          ipId: config.derivativeIpId,
          txOptions: { waitForTransaction: true },
        });

        console.log(`✅ 许可证条款附加成功`);
        console.log(`   交易哈希: ${attachResponse.txHash}`);
      } catch (error) {
        console.log(`ℹ️ 许可证条款可能已经附加: ${error}`);
      }
      console.log("");

      // 4. 铸造许可证代币
      console.log("🪙 步骤 4: 铸造许可证代币（触发收益分成）");
      console.log("-" .repeat(50));

      const mintingFeeWei = parseEther(config.mintingFee);
      const totalFee = mintingFeeWei * BigInt(config.tokenAmount);

      console.log(`单个代币费用: ${config.mintingFee} WIP`);
      console.log(`总铸造费用: ${formatEther(totalFee)} WIP`);
      console.log("");

      try {
        const mintResponse = await this.client.license.mintLicenseTokens({
          licenseTermsId: config.licenseTermsId,
          licensorIpId: config.derivativeIpId,
          amount: config.tokenAmount,
          maxMintingFee: totalFee,
          maxRevenueShare: config.revenueSharePercentage,
          txOptions: { waitForTransaction: true },
        });

        console.log(`✅ 许可证代币铸造成功!`);
        console.log(`   许可证代币 IDs: ${mintResponse.licenseTokenIds}`);
        console.log(`   交易哈希: ${mintResponse.txHash}`);
        console.log("");

        // 5. 等待收益分配
        console.log("⏳ 步骤 5: 等待收益分配");
        console.log("-" .repeat(50));
        console.log("等待 5 秒以确保收益分配完成...");
        await new Promise(resolve => setTimeout(resolve, 5000));
        console.log("");

        // 6. 检查最终余额
        console.log("📊 步骤 6: 检查最终余额");
        console.log("-" .repeat(50));

        const finalBalance = await this.getWIPBalance(this.account.address);
        const balanceChange = finalBalance - initialBalance;

        console.log(`钱包最终WIP余额: ${formatEther(finalBalance)} WIP`);
        console.log(`余额变化: ${formatEther(balanceChange)} WIP`);
        console.log("");

        // 7. 分析收益分成
        console.log("🧮 步骤 7: 收益分成分析");
        console.log("-" .repeat(50));

        const expectedCost = totalFee; // 预期支出（铸造费用）
        const expectedRevenue = (totalFee * BigInt(config.revenueSharePercentage)) / BigInt(100);
        const netExpectedChange = expectedRevenue - expectedCost;

        console.log(`预期支出（铸造费用）: ${formatEther(expectedCost)} WIP`);
        console.log(`预期收益（${config.revenueSharePercentage}%分成）: ${formatEther(expectedRevenue)} WIP`);
        console.log(`预期净变化: ${formatEther(netExpectedChange)} WIP`);
        console.log(`实际净变化: ${formatEther(balanceChange)} WIP`);
        console.log("");

        // 8. 结果评估
        console.log("✅ 步骤 8: 结果评估");
        console.log("-" .repeat(50));

        if (balanceChange > netExpectedChange) {
          console.log("🎉 收益分成测试成功！");
          console.log("✅ 实际收益超过预期，收益分成机制正常工作");
        } else if (balanceChange === -expectedCost) {
          console.log("ℹ️ 只检测到铸造费用支出");
          console.log("💡 收益分成可能以版税代币形式存在，需要手动提取");
        } else {
          console.log("⚠️ 收益分成结果需要进一步验证");
          console.log("💡 建议检查版税代币余额或等待更长时间");
        }

      } catch (mintError) {
        console.error("❌ 许可证代币铸造失败:", mintError);

        // 提供故障排除建议
        console.log("");
        console.log("🔧 故障排除建议:");
        console.log("1. 检查许可证条款ID是否正确");
        console.log("2. 确认衍生品IP ID是否正确");
        console.log("3. 确保钱包有足够的WIP代币余额");
        console.log("4. 验证许可证条款是否已附加到IP");
      }

    } catch (error) {
      console.error("💥 收益分成测试失败:", error);
    }
  }

  // 生成测试报告
  generateTestReport(): void {
    console.log("");
    console.log("📋 收益分成机制说明");
    console.log("=" .repeat(80));
    console.log("");
    console.log("🔄 收益分成流程:");
    console.log("1. 用户铸造衍生品的许可证代币");
    console.log("2. 支付铸造费用（如 0.002 WIP per token）");
    console.log("3. LAP版税政策自动计算收益分成");
    console.log("4. 根据许可证条款分配收益给原IP所有者");
    console.log("5. 剩余收益归衍生品创建者所有");
    console.log("");
    console.log("💰 收益分配示例:");
    console.log("• 铸造费用: 0.002 WIP");
    console.log("• 原IP所有者收益: 25% = 0.0005 WIP");
    console.log("• 衍生品创建者收益: 75% = 0.0015 WIP");
    console.log("");
    console.log("🔗 相关资源:");
    console.log("• Story Protocol文档: https://docs.story.foundation");
    console.log("• LAP版税政策: https://docs.story.foundation/docs/royalty-policies");
    console.log("• IP资产浏览器: https://explorer.story.foundation");
  }
}

// 主函数
async function main() {
  const CONFIG: SimpleRevenueTestConfig = {
    useMainnet: false,
    // 使用之前演示中创建的IP ID
    parentIpId: "******************************************" as Address,
    derivativeIpId: "******************************************" as Address,
    licenseTermsId: "1953", // 衍生作品许可证条款ID
    tokenAmount: 1,
    mintingFee: "0.001", // 0.001 WIP
    revenueSharePercentage: 25
  };

  if (!process.env.WALLET_PRIVATE_KEY) {
    console.error("❌ 请在 .env 文件中配置 WALLET_PRIVATE_KEY");
    return;
  }

  try {
    const demo = new SimpleRevenueSharingDemo(
      process.env.WALLET_PRIVATE_KEY,
      CONFIG.useMainnet
    );

    await demo.runSimpleRevenueTest(CONFIG);
    demo.generateTestReport();

  } catch (error) {
    console.error("💥 演示失败:", error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

export type { SimpleRevenueTestConfig };
