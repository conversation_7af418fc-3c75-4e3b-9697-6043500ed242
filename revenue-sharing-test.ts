import { type Address } from "viem/accounts";
import { parseEther, formatEther } from "viem";
import { DerivativeWorksManager } from "./derivative-works-manager";
import { MainnetCommercialLicenseManager } from "./add-commercial-license-mainnet";
import { StoryClient, type StoryConfig } from "@story-protocol/core-sdk";
import { http } from "viem";
import { privateKeyToAccount } from "viem/accounts";

/**
 * IP收益分成测试系统
 * 专门测试衍生品许可证代币铸造时的收益分成功能
 */

interface RevenueTestConfig {
  originalIpId: Address;
  derivativeIpId: Address;
  originalIpOwner: Address;
  derivativeCreator: Address;
  testTokenAmount: number;
  expectedRevenueSharePercentage: number;
}

interface RevenueTestResult {
  originalIpOwnerInitialBalance: bigint;
  originalIpOwnerFinalBalance: bigint;
  derivativeCreatorInitialBalance: bigint;
  derivativeCreatorFinalBalance: bigint;
  totalMintingFee: bigint;
  actualRevenueShare: bigint;
  expectedRevenueShare: bigint;
  revenueSharePercentage: number;
  success: boolean;
}

/**
 * 收益分成测试管理器
 */
export class RevenueSharingTester {
  private client: StoryClient;
  private derivativeManager: DerivativeWorksManager;
  private licenseManager: MainnetCommercialLicenseManager;
  private account: any;

  constructor(privateKey: string, useMainnet: boolean = false) {
    this.account = privateKeyToAccount(`0x${privateKey}` as Address);

    const config: StoryConfig = {
      account: this.account,
      transport: http(useMainnet ? "https://rpc.story.foundation" : process.env.RPC_PROVIDER_URL),
      chainId: useMainnet ? "mainnet" : "aeneid",
    };

    this.client = StoryClient.newClient(config);
    this.derivativeManager = new DerivativeWorksManager(privateKey, useMainnet);
    this.licenseManager = new MainnetCommercialLicenseManager(privateKey, useMainnet);
  }

  // 获取WIP代币余额
  async getWIPBalance(address: Address): Promise<bigint> {
    try {
      const balance = await this.client.wipClient.balanceOf(address);
      return balance;
    } catch (error) {
      console.log(`⚠️ 获取WIP余额失败: ${error}`);
      return BigInt(0);
    }
  }

  // 获取版税代币余额
  async getRoyaltyTokenBalance(ipId: Address, account: Address): Promise<bigint> {
    try {
      // 这里应该调用SDK的版税代币查询方法
      // 由于SDK可能没有直接的查询方法，我们模拟返回值
      console.log(`🔍 查询版税代币余额 (IP: ${ipId}, 账户: ${account})`);
      return BigInt(0);
    } catch (error) {
      console.log(`⚠️ 获取版税代币余额失败: ${error}`);
      return BigInt(0);
    }
  }

  // 执行详细的收益分成测试
  async runDetailedRevenueTest(config: RevenueTestConfig): Promise<RevenueTestResult> {
    console.log("💰 开始详细收益分成测试");
    console.log("=" .repeat(80));
    console.log(`原IP ID: ${config.originalIpId}`);
    console.log(`衍生品IP ID: ${config.derivativeIpId}`);
    console.log(`原IP所有者: ${config.originalIpOwner}`);
    console.log(`衍生品创建者: ${config.derivativeCreator}`);
    console.log(`测试代币数量: ${config.testTokenAmount}`);
    console.log(`预期收益分成比例: ${config.expectedRevenueSharePercentage}%`);
    console.log("");

    try {
      // 1. 记录初始余额
      console.log("📊 步骤 1: 记录初始余额");
      console.log("-" .repeat(50));

      const originalIpOwnerInitialWIP = await this.getWIPBalance(config.originalIpOwner);
      const derivativeCreatorInitialWIP = await this.getWIPBalance(config.derivativeCreator);
      const originalIpOwnerInitialRoyalty = await this.getRoyaltyTokenBalance(
        config.originalIpId,
        config.originalIpOwner
      );
      const derivativeCreatorInitialRoyalty = await this.getRoyaltyTokenBalance(
        config.derivativeIpId,
        config.derivativeCreator
      );

      console.log(`原IP所有者初始WIP余额: ${formatEther(originalIpOwnerInitialWIP)} WIP`);
      console.log(`衍生品创建者初始WIP余额: ${formatEther(derivativeCreatorInitialWIP)} WIP`);
      console.log(`原IP所有者初始版税代币: ${originalIpOwnerInitialRoyalty}`);
      console.log(`衍生品创建者初始版税代币: ${derivativeCreatorInitialRoyalty}`);
      console.log("");

      // 2. 为衍生品创建商业许可证
      console.log("📋 步骤 2: 为衍生品创建商业许可证");
      console.log("-" .repeat(50));

      const derivativeLicenseTermsId = await this.derivativeManager.createCommercialLicenseForDerivative(
        config.derivativeIpId
      );
      console.log("");

      // 3. 铸造衍生品许可证代币
      console.log("🪙 步骤 3: 铸造衍生品许可证代币");
      console.log("-" .repeat(50));

      const mintingFeePerToken = parseEther("0.002"); // 0.002 WIP per token
      const totalMintingFee = mintingFeePerToken * BigInt(config.testTokenAmount);

      console.log(`单个代币铸造费用: ${formatEther(mintingFeePerToken)} WIP`);
      console.log(`总铸造费用: ${formatEther(totalMintingFee)} WIP`);
      console.log("");

      const mintResult = await this.derivativeManager.mintDerivativeLicenseTokens(
        config.derivativeIpId,
        derivativeLicenseTermsId,
        config.testTokenAmount,
        config.derivativeCreator // 指定接收者
      );
      console.log("");

      // 4. 等待交易确认和收益分配
      console.log("⏳ 步骤 4: 等待交易确认和收益分配");
      console.log("-" .repeat(50));
      console.log("等待 5 秒以确保收益分配完成...");
      await new Promise(resolve => setTimeout(resolve, 5000));
      console.log("");

      // 5. 记录最终余额
      console.log("📊 步骤 5: 记录最终余额");
      console.log("-" .repeat(50));

      const originalIpOwnerFinalWIP = await this.getWIPBalance(config.originalIpOwner);
      const derivativeCreatorFinalWIP = await this.getWIPBalance(config.derivativeCreator);
      const originalIpOwnerFinalRoyalty = await this.getRoyaltyTokenBalance(
        config.originalIpId,
        config.originalIpOwner
      );
      const derivativeCreatorFinalRoyalty = await this.getRoyaltyTokenBalance(
        config.derivativeIpId,
        config.derivativeCreator
      );

      console.log(`原IP所有者最终WIP余额: ${formatEther(originalIpOwnerFinalWIP)} WIP`);
      console.log(`衍生品创建者最终WIP余额: ${formatEther(derivativeCreatorFinalWIP)} WIP`);
      console.log(`原IP所有者最终版税代币: ${originalIpOwnerFinalRoyalty}`);
      console.log(`衍生品创建者最终版税代币: ${derivativeCreatorFinalRoyalty}`);
      console.log("");

      // 6. 计算收益分成
      console.log("🧮 步骤 6: 计算收益分成结果");
      console.log("-" .repeat(50));

      const originalIpOwnerWIPChange = originalIpOwnerFinalWIP - originalIpOwnerInitialWIP;
      const derivativeCreatorWIPChange = derivativeCreatorFinalWIP - derivativeCreatorInitialWIP;
      const originalIpOwnerRoyaltyChange = originalIpOwnerFinalRoyalty - originalIpOwnerInitialRoyalty;

      const expectedRevenueShare = (totalMintingFee * BigInt(config.expectedRevenueSharePercentage)) / BigInt(100);
      const actualRevenueSharePercentage = totalMintingFee > 0 
        ? Number((originalIpOwnerWIPChange * BigInt(100)) / totalMintingFee)
        : 0;

      console.log(`总铸造费用: ${formatEther(totalMintingFee)} WIP`);
      console.log(`预期收益分成 (${config.expectedRevenueSharePercentage}%): ${formatEther(expectedRevenueShare)} WIP`);
      console.log(`原IP所有者WIP变化: ${formatEther(originalIpOwnerWIPChange)} WIP`);
      console.log(`衍生品创建者WIP变化: ${formatEther(derivativeCreatorWIPChange)} WIP`);
      console.log(`原IP所有者版税代币变化: ${originalIpOwnerRoyaltyChange}`);
      console.log(`实际收益分成比例: ${actualRevenueSharePercentage}%`);
      console.log("");

      // 7. 验证结果
      console.log("✅ 步骤 7: 验证收益分成结果");
      console.log("-" .repeat(50));

      const success = originalIpOwnerWIPChange > 0 || originalIpOwnerRoyaltyChange > 0;

      if (success) {
        console.log("🎉 收益分成测试成功！");
        console.log("✅ 原IP所有者成功获得了收益分成");
        
        if (originalIpOwnerWIPChange > 0) {
          console.log(`   直接WIP收益: ${formatEther(originalIpOwnerWIPChange)} WIP`);
        }
        
        if (originalIpOwnerRoyaltyChange > 0) {
          console.log(`   版税代币收益: ${originalIpOwnerRoyaltyChange} 代币`);
        }
      } else {
        console.log("⚠️ 未检测到明显的收益分成");
        console.log("   这可能是因为：");
        console.log("   1. 收益以版税代币形式存在，需要手动提取");
        console.log("   2. 收益分成有延迟处理");
        console.log("   3. 测试环境的特殊设置");
      }

      const result: RevenueTestResult = {
        originalIpOwnerInitialBalance: originalIpOwnerInitialWIP,
        originalIpOwnerFinalBalance: originalIpOwnerFinalWIP,
        derivativeCreatorInitialBalance: derivativeCreatorInitialWIP,
        derivativeCreatorFinalBalance: derivativeCreatorFinalWIP,
        totalMintingFee,
        actualRevenueShare: originalIpOwnerWIPChange,
        expectedRevenueShare,
        revenueSharePercentage: actualRevenueSharePercentage,
        success
      };

      return result;

    } catch (error) {
      console.error("💥 收益分成测试失败:", error);
      throw error;
    }
  }

  // 生成收益分成报告
  generateRevenueReport(result: RevenueTestResult): void {
    console.log("📋 收益分成测试报告");
    console.log("=" .repeat(80));
    console.log("");

    console.log("💰 财务变化摘要:");
    console.log(`• 总铸造费用: ${formatEther(result.totalMintingFee)} WIP`);
    console.log(`• 原IP所有者收益: ${formatEther(result.actualRevenueShare)} WIP`);
    console.log(`• 实际收益分成比例: ${result.revenueSharePercentage}%`);
    console.log(`• 预期收益分成比例: ${formatEther(result.expectedRevenueShare)} WIP`);
    console.log("");

    console.log("📊 详细余额变化:");
    console.log(`• 原IP所有者:`);
    console.log(`  - 初始余额: ${formatEther(result.originalIpOwnerInitialBalance)} WIP`);
    console.log(`  - 最终余额: ${formatEther(result.originalIpOwnerFinalBalance)} WIP`);
    console.log(`  - 净变化: ${formatEther(result.originalIpOwnerFinalBalance - result.originalIpOwnerInitialBalance)} WIP`);
    console.log("");
    console.log(`• 衍生品创建者:`);
    console.log(`  - 初始余额: ${formatEther(result.derivativeCreatorInitialBalance)} WIP`);
    console.log(`  - 最终余额: ${formatEther(result.derivativeCreatorFinalBalance)} WIP`);
    console.log(`  - 净变化: ${formatEther(result.derivativeCreatorFinalBalance - result.derivativeCreatorInitialBalance)} WIP`);
    console.log("");

    console.log("🎯 测试结论:");
    if (result.success) {
      console.log("✅ 收益分成机制正常工作");
      console.log("✅ 原IP所有者成功获得了预期的收益分成");
      console.log("✅ Story Protocol的LAP版税政策正确执行");
    } else {
      console.log("⚠️ 收益分成可能需要进一步验证");
      console.log("💡 建议检查版税代币余额或等待更长时间");
    }
    console.log("");

    console.log("🔗 收益分成机制说明:");
    console.log("• Story Protocol使用LAP (Liquid Absolute Percentage) 版税政策");
    console.log("• 收益分成在许可证代币铸造时自动执行");
    console.log("• 收益可能以版税代币形式分配，需要手动提取");
    console.log("• 支持多级衍生品的递归收益分成");
  }
}

// 主函数 - 使用示例
async function main() {
  // 配置参数 - 请根据实际情况修改
  const CONFIG = {
    useMainnet: false, // 设置为 true 使用主网
    
    // 测试参数 - 需要替换为实际的IP ID和地址
    originalIpId: "0x你的原始IP_ID" as Address,
    derivativeIpId: "0x你的衍生品IP_ID" as Address,
    originalIpOwner: "0x你的原始IP所有者地址" as Address,
    derivativeCreator: "0x你的衍生品创建者地址" as Address,
    testTokenAmount: 2,
    expectedRevenueSharePercentage: 25
  };

  // 验证配置
  if (CONFIG.originalIpId === "0x你的原始IP_ID") {
    console.error("❌ 请先配置正确的IP ID和地址");
    console.log("💡 运行 derivative-license-revenue-demo.ts 获取实际的IP ID");
    process.exit(1);
  }

  if (!process.env.WALLET_PRIVATE_KEY) {
    console.error("❌ 请在 .env 文件中配置 WALLET_PRIVATE_KEY");
    process.exit(1);
  }

  try {
    const tester = new RevenueSharingTester(
      process.env.WALLET_PRIVATE_KEY,
      CONFIG.useMainnet
    );

    const result = await tester.runDetailedRevenueTest(CONFIG);
    tester.generateRevenueReport(result);

  } catch (error) {
    console.error("💥 测试失败:", error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

export { RevenueSharingTester, type RevenueTestConfig, type RevenueTestResult };
