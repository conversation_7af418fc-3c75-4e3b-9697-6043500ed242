import { runDerivativeWorksDemo } from "./derivative-works-demo";

/**
 * 测试修复后的衍生品创建功能
 */
async function testDerivativeFix() {
  console.log("🧪 测试修复后的衍生品创建功能");
  console.log("=" .repeat(60));
  console.log("");
  
  console.log("📝 修复内容:");
  console.log("1. ✅ 修复了 registerDerivative 中使用错误参数的问题");
  console.log("2. ✅ 现在使用许可证条款ID而不是许可证代币ID");
  console.log("3. ✅ 衍生品创建不再依赖许可证代币铸造成功");
  console.log("4. ✅ 添加了更详细的错误信息和调试输出");
  console.log("");
  
  console.log("🚀 开始运行修复后的演示...");
  console.log("");
  
  try {
    await runDerivativeWorksDemo();
  } catch (error) {
    console.error("❌ 测试失败:", error);
  }
}

// 运行测试
if (require.main === module) {
  testDerivativeFix().catch(console.error);
}

export { testDerivativeFix };
