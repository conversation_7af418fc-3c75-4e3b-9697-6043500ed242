import { type Address } from "viem/accounts";
import { zeroAddress } from "viem";
import { DerivativeWorksManager, type DerivativeMetadata, uploadJSONToIPFS } from "./derivative-works-manager";
import { MainnetCommercialLicenseManager } from "./add-commercial-license-mainnet";
import { StoryClient, type StoryConfig } from "@story-protocol/core-sdk";
import { http } from "viem";
import { privateKeyToAccount } from "viem/accounts";
import { createHash } from "crypto";

/**
 * IP衍生品完整演示系统
 * 展示从创建原始IP、铸造许可证代币到创建衍生品的完整流程
 */

// 演示配置
const DEMO_CONFIG = {
  // 网络设置
  useMainnet: false, // 设置为 true 使用主网

  // 原始IP配置
  originalIP: {
    name: "原创艺术作品 #001",
    description: "这是一个支持衍生作品创作的原创艺术作品",
    imageUrl: "https://picsum.photos/id/100/800/600",
    includeCommercial: true,
    includeLimitedCommercial: false,
    includeDerivativeWorks: true, // 关键：支持衍生作品
    autoMintTokens: true,
    tokenAmount: 3 // 铸造3个许可证代币用于演示
  },

  // 衍生品配置
  derivatives: [
    {
      title: "数字艺术重制版",
      description: "基于原作品创作的数字艺术重制版，添加了现代元素",
      imageUrl: "https://picsum.photos/id/101/800/600",
      derivativeType: "digital_remix",
      changes: "添加了数字特效和现代色彩调整"
    },
    {
      title: "风格化改编作品",
      description: "将原作品改编为不同艺术风格的版本",
      imageUrl: "https://picsum.photos/id/102/800/600",
      derivativeType: "style_adaptation",
      changes: "改变了艺术风格，采用了抽象表现主义手法"
    }
  ]
};

async function runDerivativeWorksDemo() {
  console.log("🎭 IP衍生品完整演示系统");
  console.log("=" .repeat(80));
  console.log("本演示展示从创建原始IP到创建衍生品的完整流程");
  console.log("");

  // 检查环境变量
  if (!process.env.WALLET_PRIVATE_KEY) {
    console.error("❌ 请在 .env 文件中配置 WALLET_PRIVATE_KEY");
    console.log("💡 创建 .env 文件并添加:");
    console.log("   WALLET_PRIVATE_KEY=你的钱包私钥");
    console.log("   RPC_PROVIDER_URL=你的RPC地址");
    return;
  }

  try {
    // 初始化客户端和管理器
    console.log("🔧 初始化系统组件...");
    const account = privateKeyToAccount(`0x${process.env.WALLET_PRIVATE_KEY}` as Address);
    const config: StoryConfig = {
      account: account,
      transport: http(DEMO_CONFIG.useMainnet ? "https://rpc.story.foundation" : process.env.RPC_PROVIDER_URL),
      chainId: DEMO_CONFIG.useMainnet ? "mainnet" : "aeneid",
    };
    const client = StoryClient.newClient(config);

    const licenseManager = new MainnetCommercialLicenseManager(
      process.env.WALLET_PRIVATE_KEY,
      DEMO_CONFIG.useMainnet
    );

    const derivativeManager = new DerivativeWorksManager(
      process.env.WALLET_PRIVATE_KEY,
      DEMO_CONFIG.useMainnet
    );

    console.log(`✅ 系统初始化完成 (${DEMO_CONFIG.useMainnet ? "主网" : "测试网"})`);
    console.log("");

    // 步骤 1: 创建NFT集合
    console.log("🎨 步骤 1: 创建NFT集合");
    console.log("=" .repeat(60));

    const collection = await client.nftClient.createNFTCollection({
      name: "Derivative Works Demo Collection",
      symbol: "DWDC",
      isPublicMinting: false,
      mintOpen: true,
      mintFeeRecipient: zeroAddress,
      contractURI: "https://example.com/derivative-collection.json",
      txOptions: { waitForTransaction: true },
    });

    console.log(`✅ NFT集合创建成功`);
    console.log(`   集合地址: ${collection.spgNftContract}`);
    console.log("");

    // 步骤 2: 创建原始IP并添加支持衍生作品的许可证
    console.log("🖼️ 步骤 2: 创建原始IP（支持衍生作品）");
    console.log("=" .repeat(60));

    // 注册衍生作品许可证条款
    await derivativeManager.registerDerivativeWorksLicense();

    // 创建原始IP（这里需要手动创建支持衍生作品的IP）
    const originalIPMetadata = client.ipAsset.generateIpMetadata({
      title: DEMO_CONFIG.originalIP.name,
      description: DEMO_CONFIG.originalIP.description,
      watermarkImg: DEMO_CONFIG.originalIP.imageUrl,
    });

    const originalNFTMetadata = {
      name: DEMO_CONFIG.originalIP.name,
      description: DEMO_CONFIG.originalIP.description,
      image: DEMO_CONFIG.originalIP.imageUrl,
    };

    // 上传元数据
    const ipIpfsHash = await uploadJSONToIPFS(originalIPMetadata);
    const ipHash = createHash("sha256")
      .update(JSON.stringify(originalIPMetadata))
      .digest("hex");
    const nftIpfsHash = await uploadJSONToIPFS(originalNFTMetadata);
    const nftHash = createHash("sha256")
      .update(JSON.stringify(originalNFTMetadata))
      .digest("hex");

    // 创建原始IP
    const originalIPResponse = await client.ipAsset.mintAndRegisterIp({
      spgNftContract: collection.spgNftContract as Address,
      allowDuplicates: true,
      ipMetadata: {
        ipMetadataURI: `https://ipfs.io/ipfs/${ipIpfsHash}`,
        ipMetadataHash: `0x${ipHash}`,
        nftMetadataURI: `https://ipfs.io/ipfs/${nftIpfsHash}`,
        nftMetadataHash: `0x${nftHash}`,
      },
      txOptions: { waitForTransaction: true },
    });

    console.log(`✅ 原始IP创建成功`);
    console.log(`   IP ID: ${originalIPResponse.ipId}`);
    console.log(`   Token ID: ${originalIPResponse.tokenId}`);
    console.log(`   交易哈希: ${originalIPResponse.txHash}`);
    console.log("");

    // 步骤 3: 为原始IP添加衍生作品许可证
    console.log("📋 步骤 3: 为原始IP添加衍生作品许可证");
    console.log("=" .repeat(60));

    const derivativeLicenseTermsId = derivativeManager.getDerivativeLicenseTermsId();
    if (derivativeLicenseTermsId && originalIPResponse.ipId) {
      try {
        const attachResponse = await client.license.attachLicenseTerms({
          licenseTermsId: derivativeLicenseTermsId.toString(),
          ipId: originalIPResponse.ipId,
          txOptions: { waitForTransaction: true },
        });

        console.log(`✅ 衍生作品许可证已附加到原始IP`);
        console.log(`   交易哈希: ${attachResponse.txHash}`);
      } catch (error) {
        console.log(`ℹ️ 许可证可能已经附加: ${error}`);
      }
    }
    console.log("");

    // 步骤 4: 铸造衍生作品许可证代币
    console.log("🪙 步骤 4: 铸造衍生作品许可证代币");
    console.log("=" .repeat(60));

    let licenseTokenIds: bigint[] = [];

    if (derivativeLicenseTermsId && originalIPResponse.ipId) {
      try {
        const mintResponse = await client.license.mintLicenseTokens({
          licenseTermsId: derivativeLicenseTermsId.toString(),
          licensorIpId: originalIPResponse.ipId,
          amount: DEMO_CONFIG.originalIP.tokenAmount,
          maxMintingFee: "5000000000000000000", // 5 WIP
          maxRevenueShare: 25, // 25%
          txOptions: { waitForTransaction: true },
        });

        licenseTokenIds = mintResponse.licenseTokenIds || [];
        console.log(`✅ 许可证代币铸造成功`);
        console.log(`   代币数量: ${licenseTokenIds.length}`);
        console.log(`   代币 IDs: ${licenseTokenIds.join(", ")}`);
        console.log(`   交易哈希: ${mintResponse.txHash}`);
      } catch (error) {
        console.log(`⚠️ 许可证代币铸造失败: ${error}`);
        console.log("   将使用模拟代币ID进行演示");
        licenseTokenIds = [BigInt(1), BigInt(2)]; // 模拟代币ID
      }
    }
    console.log("");

    // 步骤 5: 创建衍生品
    console.log("🎭 步骤 5: 创建衍生品");
    console.log("=" .repeat(60));

    const derivativeResults = [];

    // 创建衍生品不再依赖许可证代币，直接使用许可证条款
    for (let i = 0; i < DEMO_CONFIG.derivatives.length; i++) {
      const derivativeConfig = DEMO_CONFIG.derivatives[i];

      console.log(`🔄 创建衍生品 ${i + 1}/${DEMO_CONFIG.derivatives.length}: ${derivativeConfig.title}`);
      console.log("-" .repeat(50));

      try {
        const derivativeMetadata: DerivativeMetadata = {
          title: derivativeConfig.title,
          description: derivativeConfig.description,
          imageUrl: derivativeConfig.imageUrl,
          parentIpId: originalIPResponse.ipId!,
          derivativeType: derivativeConfig.derivativeType,
          changes: derivativeConfig.changes
        };

        // 使用铸造的许可证代币（如果有的话）
        const tokensToUse = licenseTokenIds.length > i ? [licenseTokenIds[i]] : [];
        const derivativeResult = await derivativeManager.createDerivativeWork(
          collection.spgNftContract as Address,
          derivativeMetadata,
          tokensToUse // 使用对应的许可证代币
        );

        derivativeResults.push(derivativeResult);

        console.log(`✅ 衍生品 ${i + 1} 创建成功`);
        console.log(`   衍生品 IP ID: ${derivativeResult.ipId}`);
        console.log(`   父IP ID: ${originalIPResponse.ipId}`);
        console.log(`   使用许可证条款ID: ${derivativeManager.getDerivativeLicenseTermsId()}`);
        if (tokensToUse.length > 0) {
          console.log(`   使用许可证代币: ${tokensToUse.join(", ")}`);
        } else {
          console.log(`   使用许可证代币: 无（直接使用许可证条款）`);
        }
        console.log("");

      } catch (error) {
        console.log(`❌ 衍生品 ${i + 1} 创建失败: ${error}`);
        console.log("");
      }

      // 添加延迟避免请求过快
      if (i < DEMO_CONFIG.derivatives.length - 1) {
        console.log("⏳ 等待 3 秒后创建下一个衍生品...");
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    }

    // 显示最终总结
    console.log("🎉 IP衍生品演示完成！");
    console.log("=" .repeat(80));
    console.log("");
    console.log("📊 演示总结:");
    console.log(`✅ 原始IP: ${originalIPResponse.ipId}`);
    console.log(`✅ 铸造的许可证代币: ${licenseTokenIds.length} 个`);
    console.log(`✅ 创建的衍生品: ${derivativeResults.length} 个`);
    console.log("");

    console.log("🔗 父子IP关系:");
    derivativeResults.forEach((result, index) => {
      console.log(`${index + 1}. 衍生品: ${result.ipId}`);
      console.log(`   └── 父IP: ${originalIPResponse.ipId}`);
      console.log(`   └── 许可证代币: ${result.licenseTokenIds?.join(", ")}`);
    });
    console.log("");

    console.log("💰 收益分成配置:");
    console.log("• 衍生品产生商业收益时，25% 自动分配给原始IP所有者");
    console.log("• 衍生品创建者保留 75% 的收益");
    console.log("• 收益分成通过智能合约自动执行");
    console.log("");

    console.log("🌐 查看详情:");
    console.log(`• 原始IP: https://explorer.story.foundation/ipa/${originalIPResponse.ipId}`);
    derivativeResults.forEach((result, index) => {
      console.log(`• 衍生品 ${index + 1}: https://explorer.story.foundation/ipa/${result.ipId}`);
    });

  } catch (error) {
    console.error("💥 演示执行失败:", error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runDerivativeWorksDemo().catch(console.error);
}

export { runDerivativeWorksDemo, DEMO_CONFIG };
