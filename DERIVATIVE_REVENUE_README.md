# IP衍生品许可证和收益分成演示

这个演示系统展示了Story Protocol中IP衍生品的许可证创建和收益分成机制。当衍生品的许可证代币被铸造时，原始IP所有者会自动获得收益分成。

## 🎯 功能特性

### 核心功能
- ✅ 创建支持衍生作品的原始IP
- ✅ 基于原始IP创建衍生品
- ✅ 为衍生品创建商业许可证
- ✅ 铸造衍生品的许可证代币
- ✅ 自动收益分成机制
- ✅ 多级衍生品支持

### 收益分成机制
- 🔄 **自动执行**: 通过LAP版税政策智能合约自动执行
- 💰 **即时分配**: 许可证代币铸造时立即触发收益分成
- 📊 **透明计算**: 收益分成比例在许可证条款中明确定义
- 🌐 **多级支持**: 支持多级衍生品的递归收益分成

## 🚀 快速开始

### 1. 环境准备

确保已安装依赖并配置环境变量：

```bash
# 安装依赖
bun install

# 配置环境变量（创建.env文件）
WALLET_PRIVATE_KEY=你的钱包私钥（不包含0x前缀）
RPC_PROVIDER_URL=你的RPC地址
PINATA_JWT=你的Pinata JWT令牌
```

### 2. 运行完整演示

```bash
# 运行衍生品许可证和收益分成演示
bun run derivative-revenue

# 或者使用启动脚本
bun run run-derivative-demo
```

### 3. 单独测试收益分成

```bash
# 运行收益分成测试（需要先有IP ID）
bun run revenue-test
```

## 📋 演示流程

### 步骤1: 创建原始IP
- 创建NFT集合
- 铸造并注册原始IP
- 添加支持衍生作品的许可证条款

### 步骤2: 创建衍生品
- 基于原始IP创建衍生品
- 建立父子IP关系
- 继承收益分成设置

### 步骤3: 衍生品许可证
- 为衍生品创建商业许可证
- 设置收益分成比例（通常25%给原IP）
- 附加许可证条款到衍生品IP

### 步骤4: 铸造许可证代币
- 铸造衍生品的许可证代币
- 触发自动收益分成
- 验证原IP所有者收益

### 步骤5: 收益分成验证
- 查询余额变化
- 计算收益分成比例
- 生成详细报告

## 💰 收益分成详情

### 分成比例
- **原IP所有者**: 25% 的铸造费用
- **衍生品创建者**: 75% 的铸造费用
- **铸造费用**: 0.002 WIP per token

### 分成机制
1. **触发时机**: 衍生品许可证代币铸造时
2. **执行方式**: LAP版税政策智能合约自动执行
3. **分配形式**: 可能以WIP代币或版税代币形式
4. **提取方式**: 部分收益可能需要手动提取

### 多级衍生品
```
原始IP (Alice)
└── 衍生品1 (Bob) - 25% → Alice
    └── 衍生品2 (Charlie) - 25% → Bob, 递归分成 → Alice
        └── 衍生品3 (David) - 继续递归...
```

## 📊 输出示例

### 成功的收益分成
```
💰 收益分成结果:
========================================
铸造费用总额: 0.004 WIP (2 × 0.002 WIP)
预期收益分成: 25%
预期分成金额: 0.001 WIP
实际版税代币增加: 1000000000000000

✅ 收益分成功能正常工作！
原IP所有者成功获得了衍生品许可证代币铸造的收益分成
```

### 详细财务报告
```
📋 收益分成测试报告
================================================================================

💰 财务变化摘要:
• 总铸造费用: 0.004 WIP
• 原IP所有者收益: 0.001 WIP
• 实际收益分成比例: 25%
• 预期收益分成比例: 0.001 WIP

📊 详细余额变化:
• 原IP所有者:
  - 初始余额: 10.0 WIP
  - 最终余额: 10.001 WIP
  - 净变化: +0.001 WIP

• 衍生品创建者:
  - 初始余额: 5.0 WIP
  - 最终余额: 4.997 WIP
  - 净变化: -0.003 WIP (支付铸造费用)
```

## 🔧 配置选项

### 网络设置
```typescript
const DEMO_CONFIG = {
  useMainnet: false, // true = 主网, false = 测试网
  // ...其他配置
};
```

### 许可证配置
```typescript
const derivativeCommercialLicense = {
  transferable: true,
  defaultMintingFee: parseEther("0.002"), // 0.002 WIP
  commercialRevShare: 20, // 20% 给衍生品创建者
  derivativesAllowed: true, // 允许进一步衍生
  // ...其他设置
};
```

## 🛠️ 故障排除

### 常见问题

1. **余额不足**
   ```
   ❌ 余额不足：需要 0.002 WIP
   💡 建议：确保钱包有足够的 WIP 代币余额
   ```

2. **许可证已存在**
   ```
   ℹ️ 许可证可能已经附加: LicenseTermsAlreadyAttached
   ```

3. **收益分成未检测到**
   ```
   ℹ️ 未检测到版税代币余额变化
   这可能是因为：
   1. 版税代币需要手动提取
   2. 收益分成有延迟
   3. SDK查询方法需要更新
   ```

### 解决方案

1. **检查环境配置**
   - 确认.env文件配置正确
   - 验证钱包私钥格式（不包含0x前缀）
   - 确认RPC_PROVIDER_URL可访问

2. **确保足够余额**
   - 检查WIP代币余额
   - 预留足够的gas费用

3. **网络连接**
   - 确认网络连接稳定
   - 尝试切换RPC节点

## 📚 相关资源

- [Story Protocol文档](https://docs.story.foundation)
- [TypeScript SDK文档](https://docs.story.foundation/docs/typescript-sdk)
- [IP资产浏览器](https://explorer.story.foundation)
- [LAP版税政策说明](https://docs.story.foundation/docs/royalty-policies)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个演示系统！

## 📄 许可证

MIT License
