#!/usr/bin/env npx tsx

import { runDerivativeLicenseRevenueDemo } from "./derivative-license-revenue-demo";

/**
 * 衍生品许可证和收益分成演示启动脚本
 * 
 * 这个脚本将运行完整的演示，包括：
 * 1. 创建原始IP
 * 2. 创建衍生品
 * 3. 为衍生品创建许可证
 * 4. 铸造衍生品许可证代币
 * 5. 测试收益分成功能
 */

async function main() {
  console.log("🚀 启动IP衍生品许可证和收益分成演示");
  console.log("=" .repeat(80));
  console.log("");
  
  console.log("📋 演示内容:");
  console.log("✅ 创建支持衍生作品的原始IP");
  console.log("✅ 基于原始IP创建衍生品");
  console.log("✅ 为衍生品创建商业许可证");
  console.log("✅ 铸造衍生品的许可证代币");
  console.log("✅ 测试收益分成机制");
  console.log("✅ 验证原IP所有者获得收益分成");
  console.log("");
  
  console.log("💡 演示说明:");
  console.log("• 本演示展示了Story Protocol的多级IP收益分成机制");
  console.log("• 当衍生品的许可证代币被铸造时，原IP所有者会自动获得收益分成");
  console.log("• 收益分成比例根据许可证条款自动执行（通常为25%）");
  console.log("• 支持多级衍生品的递归收益分成");
  console.log("");
  
  console.log("⚠️ 注意事项:");
  console.log("• 确保钱包有足够的WIP代币余额");
  console.log("• 确保已正确配置.env文件");
  console.log("• 演示过程可能需要几分钟时间");
  console.log("");
  
  console.log("🔄 开始执行演示...");
  console.log("");

  try {
    await runDerivativeLicenseRevenueDemo();
    
    console.log("");
    console.log("🎉 演示执行完成！");
    console.log("");
    console.log("📚 下一步建议:");
    console.log("1. 查看浏览器中的IP资产详情");
    console.log("2. 验证收益分成是否正确执行");
    console.log("3. 尝试创建更多级别的衍生品");
    console.log("4. 测试不同类型的许可证条款");
    console.log("");
    console.log("🔗 相关资源:");
    console.log("• Story Protocol文档: https://docs.story.foundation");
    console.log("• IP资产浏览器: https://explorer.story.foundation");
    console.log("• SDK文档: https://docs.story.foundation/docs/typescript-sdk");
    
  } catch (error) {
    console.error("");
    console.error("💥 演示执行失败:");
    console.error(error);
    console.error("");
    console.error("🔧 故障排除建议:");
    console.error("1. 检查.env文件配置是否正确");
    console.error("2. 确认钱包私钥格式正确（不包含0x前缀）");
    console.error("3. 确认网络连接正常");
    console.error("4. 确认钱包有足够的WIP代币余额");
    console.error("5. 检查RPC_PROVIDER_URL是否可访问");
    console.error("");
    process.exit(1);
  }
}

// 运行主函数
main().catch((error) => {
  console.error("💥 启动脚本失败:", error);
  process.exit(1);
});
