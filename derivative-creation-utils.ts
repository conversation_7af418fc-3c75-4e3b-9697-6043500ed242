import { type Address } from "viem/accounts";
import { DerivativeWorksManager, type DerivativeMetadata, type DerivativeCreationResult } from "./derivative-works-manager";
import { MainnetCommercialLicenseManager } from "./add-commercial-license-mainnet";

/**
 * 衍生品创建工具函数
 * 提供简化的API用于创建IP衍生品
 */

// 衍生品创建选项接口
export interface DerivativeCreationOptions {
  parentIpId: Address;
  spgNftContract: Address;
  derivativeMetadata: {
    title: string;
    description: string;
    imageUrl: string;
    derivativeType: string;
    changes: string;
  };
  licenseTokenIds: bigint[];
  useMainnet?: boolean;
}

// 批量衍生品创建选项接口
export interface BatchDerivativeCreationOptions {
  parentIpId: Address;
  spgNftContract: Address;
  derivatives: Array<{
    title: string;
    description: string;
    imageUrl: string;
    derivativeType: string;
    changes: string;
    licenseTokenId: bigint;
  }>;
  useMainnet?: boolean;
}

/**
 * 创建单个衍生品IP
 * @param options 衍生品创建选项
 * @returns 衍生品创建结果
 */
export async function createSingleDerivative(
  options: DerivativeCreationOptions
): Promise<DerivativeCreationResult> {
  if (!process.env.WALLET_PRIVATE_KEY) {
    throw new Error("请在 .env 文件中配置 WALLET_PRIVATE_KEY");
  }

  const {
    parentIpId,
    spgNftContract,
    derivativeMetadata,
    licenseTokenIds,
    useMainnet = false
  } = options;

  console.log("🎭 创建单个衍生品IP");
  console.log("=" .repeat(50));
  console.log(`父IP ID: ${parentIpId}`);
  console.log(`衍生品标题: ${derivativeMetadata.title}`);
  console.log(`衍生品类型: ${derivativeMetadata.derivativeType}`);
  console.log(`使用的许可证代币: ${licenseTokenIds.join(", ")}`);
  console.log("");

  try {
    // 创建衍生品管理器
    const derivativeManager = new DerivativeWorksManager(
      process.env.WALLET_PRIVATE_KEY,
      useMainnet
    );

    // 准备衍生品元数据
    const metadata: DerivativeMetadata = {
      title: derivativeMetadata.title,
      description: derivativeMetadata.description,
      imageUrl: derivativeMetadata.imageUrl,
      parentIpId: parentIpId,
      derivativeType: derivativeMetadata.derivativeType,
      changes: derivativeMetadata.changes
    };

    // 创建衍生品
    const result = await derivativeManager.createDerivativeWork(
      spgNftContract,
      metadata,
      licenseTokenIds
    );

    console.log("✅ 衍生品创建成功！");
    console.log(`   衍生品 IP ID: ${result.ipId}`);
    console.log(`   Token ID: ${result.tokenId}`);
    console.log(`   交易哈希: ${result.txHash}`);
    console.log("");

    return result;

  } catch (error) {
    console.error("❌ 衍生品创建失败:", error);
    throw error;
  }
}

/**
 * 批量创建衍生品IP
 * @param options 批量衍生品创建选项
 * @returns 衍生品创建结果数组
 */
export async function createBatchDerivatives(
  options: BatchDerivativeCreationOptions
): Promise<Array<{
  success: boolean;
  derivative: any;
  result?: DerivativeCreationResult;
  error?: any;
}>> {
  if (!process.env.WALLET_PRIVATE_KEY) {
    throw new Error("请在 .env 文件中配置 WALLET_PRIVATE_KEY");
  }

  const {
    parentIpId,
    spgNftContract,
    derivatives,
    useMainnet = false
  } = options;

  console.log("🎭 批量创建衍生品IP");
  console.log("=" .repeat(50));
  console.log(`父IP ID: ${parentIpId}`);
  console.log(`衍生品数量: ${derivatives.length}`);
  console.log("");

  const results = [];

  // 创建衍生品管理器
  const derivativeManager = new DerivativeWorksManager(
    process.env.WALLET_PRIVATE_KEY,
    useMainnet
  );

  for (let i = 0; i < derivatives.length; i++) {
    const derivative = derivatives[i];

    console.log(`🔄 创建衍生品 ${i + 1}/${derivatives.length}: ${derivative.title}`);
    console.log("-" .repeat(30));

    try {
      // 准备衍生品元数据
      const metadata: DerivativeMetadata = {
        title: derivative.title,
        description: derivative.description,
        imageUrl: derivative.imageUrl,
        parentIpId: parentIpId,
        derivativeType: derivative.derivativeType,
        changes: derivative.changes
      };

      // 创建衍生品
      const result = await derivativeManager.createDerivativeWork(
        spgNftContract,
        metadata,
        [derivative.licenseTokenId]
      );

      results.push({
        success: true,
        derivative,
        result
      });

      console.log(`✅ 衍生品 ${i + 1} 创建成功`);
      console.log(`   IP ID: ${result.ipId}`);
      console.log("");

    } catch (error) {
      results.push({
        success: false,
        derivative,
        error
      });

      console.log(`❌ 衍生品 ${i + 1} 创建失败: ${error}`);
      console.log("");
    }

    // 添加延迟避免请求过快
    if (i < derivatives.length - 1) {
      console.log("⏳ 等待 2 秒后创建下一个衍生品...");
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }

  // 显示批量创建总结
  const successCount = results.filter(r => r.success).length;
  const failureCount = results.filter(r => !r.success).length;

  console.log("📊 批量创建总结:");
  console.log(`✅ 成功创建: ${successCount} 个衍生品`);
  console.log(`❌ 创建失败: ${failureCount} 个衍生品`);
  console.log("");

  return results;
}

/**
 * 为现有IP创建衍生品（使用已有的许可证代币）
 * @param parentIpId 父IP ID
 * @param licenseTokenId 许可证代币ID
 * @param derivativeInfo 衍生品信息
 * @param spgNftContract NFT合约地址
 * @param useMainnet 是否使用主网
 * @returns 衍生品创建结果
 */
export async function createDerivativeFromExistingIP(
  parentIpId: Address,
  licenseTokenId: bigint,
  derivativeInfo: {
    title: string;
    description: string;
    imageUrl: string;
    derivativeType: string;
    changes: string;
  },
  spgNftContract: Address,
  useMainnet: boolean = false
): Promise<DerivativeCreationResult> {
  console.log("🔗 基于现有IP创建衍生品");
  console.log("=" .repeat(50));
  console.log(`父IP ID: ${parentIpId}`);
  console.log(`许可证代币ID: ${licenseTokenId}`);
  console.log(`衍生品标题: ${derivativeInfo.title}`);
  console.log("");

  return await createSingleDerivative({
    parentIpId,
    spgNftContract,
    derivativeMetadata: derivativeInfo,
    licenseTokenIds: [licenseTokenId],
    useMainnet
  });
}

/**
 * 验证许可证代币是否可用于创建衍生品
 * @param licenseTokenId 许可证代币ID
 * @param parentIpId 父IP ID
 * @param useMainnet 是否使用主网
 * @returns 是否有效
 */
export async function validateLicenseTokenForDerivative(
  licenseTokenId: bigint,
  parentIpId: Address,
  useMainnet: boolean = false
): Promise<boolean> {
  if (!process.env.WALLET_PRIVATE_KEY) {
    throw new Error("请在 .env 文件中配置 WALLET_PRIVATE_KEY");
  }

  console.log("🔍 验证许可证代币有效性");
  console.log(`许可证代币ID: ${licenseTokenId}`);
  console.log(`父IP ID: ${parentIpId}`);

  try {
    const derivativeManager = new DerivativeWorksManager(
      process.env.WALLET_PRIVATE_KEY,
      useMainnet
    );

    const isValid = await derivativeManager.validateLicenseToken(licenseTokenId, parentIpId);
    
    if (isValid) {
      console.log("✅ 许可证代币验证通过");
    } else {
      console.log("❌ 许可证代币验证失败");
    }

    return isValid;

  } catch (error) {
    console.error("❌ 验证过程出错:", error);
    return false;
  }
}

/**
 * 查询IP的衍生品关系
 * @param ipId IP ID
 * @param useMainnet 是否使用主网
 * @returns 关系信息
 */
export async function queryIPRelationships(
  ipId: Address,
  useMainnet: boolean = false
): Promise<{
  derivatives: Address[];
  parents: Address[];
}> {
  if (!process.env.WALLET_PRIVATE_KEY) {
    throw new Error("请在 .env 文件中配置 WALLET_PRIVATE_KEY");
  }

  console.log("🔍 查询IP关系");
  console.log(`IP ID: ${ipId}`);

  try {
    const derivativeManager = new DerivativeWorksManager(
      process.env.WALLET_PRIVATE_KEY,
      useMainnet
    );

    const derivatives = await derivativeManager.getDerivatives(ipId);
    const parents = await derivativeManager.getParentIPs(ipId);

    console.log(`✅ 查询完成`);
    console.log(`   衍生品数量: ${derivatives.length}`);
    console.log(`   父IP数量: ${parents.length}`);

    return {
      derivatives,
      parents
    };

  } catch (error) {
    console.error("❌ 查询失败:", error);
    return {
      derivatives: [],
      parents: []
    };
  }
}
